# 🔐 Admin Setup Guide

## How Admins Access the Verification Dashboard

Admins use the **same login system** as landlords - no separate admin portal needed!

## 📋 Setup Steps

### **Step 1: Run the Verification Migration**
```bash
# Apply the verification system to your existing database
psql -d your_database -f verification-system-migration.sql
```

### **Step 2: Create Admin Users**

#### **Option A: Promote Existing User**
```sql
-- Replace '<EMAIL>' with the actual admin email
UPDATE public.profiles 
SET 
  user_role = 'super_admin',
  verification_status = 'verified',
  trust_score = 5.0,
  admin_notes = 'System Administrator'
WHERE id = (
  SELECT id FROM auth.users 
  WHERE email = '<EMAIL>'
  LIMIT 1
);
```

#### **Option B: Create New Admin Account**
1. Have the admin sign up through your normal registration process
2. Get their user ID from the database
3. Run the SQL above with their email

### **Step 3: Admin Login Flow**

1. **Admin logs in** using regular login page (`/auth`)
2. **System detects admin role** from their profile
3. **Admin dashboard link appears** in navigation menu
4. **Admin clicks "Admin Dashboard"** to access verification tools

## 🎯 Admin Access Levels

### **Super Admin (`super_admin`)**
- Full access to all verification features
- Can manage other admins
- Access to all fraud reports and statistics

### **Regular Admin (`admin`)**
- Can review landlord verifications
- Can manage fraud reports
- Cannot manage other admins

## 🔍 How to Identify Admins

### **In Navigation Menu:**
- Admin users see an **"Admin Dashboard"** link
- Mobile menu shows **"Administrator"** badge
- Desktop navigation shows **shield icon**

### **In Database:**
```sql
-- Check all admin users
SELECT 
  au.email,
  p.full_name,
  p.user_role,
  p.verification_status
FROM auth.users au
JOIN public.profiles p ON au.id = p.id
WHERE p.user_role IN ('admin', 'super_admin')
ORDER BY p.user_role DESC;
```

## 🛡️ Admin Dashboard Features

Once logged in, admins can access:

### **📊 Overview Dashboard**
- Total landlords and verification statistics
- Pending verifications count
- Active fraud reports
- System health indicators

### **👥 Verification Management**
- Review pending landlord applications
- Approve/reject identity documents
- Set trust scores (1.0 - 5.0)
- Add admin notes

### **🚨 Fraud Report Management**
- View all fraud reports
- Investigate with evidence review
- Update report status
- Add resolution notes

### **🏠 Property Oversight**
- Approve/reject property listings
- Verify ownership documentation
- Monitor property authenticity

## 🔄 Admin Workflow

### **Daily Admin Tasks:**
1. **Check Dashboard** - Review pending items
2. **Process Verifications** - Approve/reject applications
3. **Handle Fraud Reports** - Investigate and resolve
4. **Monitor Statistics** - Track system health

### **Verification Process:**
1. Landlord submits verification
2. Admin reviews documents
3. Admin sets trust score
4. Admin approves/rejects
5. Landlord gets notification
6. Trust badge appears on listings

## 🚀 Quick Start

### **For New Installations:**
1. Run `complete-database-setup.sql` (includes verification)
2. Create admin user (see Step 2 above)
3. Admin logs in and sees dashboard link

### **For Existing Installations:**
1. Run `verification-system-migration.sql`
2. Create admin user (see Step 2 above)
3. Admin logs in and sees dashboard link

## 🔧 Troubleshooting

### **Admin Can't See Dashboard Link:**
- Check user_role in profiles table
- Ensure profile exists for the user
- Verify user is logged in

### **Access Denied Error:**
- Confirm user_role is 'admin' or 'super_admin'
- Check if profile was created properly
- Try logging out and back in

### **No Pending Verifications:**
- Encourage landlords to complete verification
- Check if verification_status is set correctly
- Verify RLS policies are working

## 📞 Support

If you need help:
1. Check the database queries above
2. Verify the migration ran successfully
3. Ensure admin users have correct roles
4. Test with a known admin account

The admin system is now fully integrated with your existing authentication - no separate login required! 🎉
