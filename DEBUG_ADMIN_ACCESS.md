# 🔍 Debug Admin Access Issues

## Current Issue
User updated their role to `super_admin` but still sees the landlord page instead of being redirected to admin dashboard.

## 🔧 Debugging Steps

### **Step 1: Verify Database Update**
Run this query to confirm the user role was updated:

```sql
SELECT 
  au.email,
  p.full_name,
  p.user_role,
  p.verification_status,
  p.trust_score,
  p.updated_at
FROM auth.users au
JOIN public.profiles p ON au.id = p.id
WHERE au.email = 'adan<PERSON><EMAIL>';
```

**Expected Result:** `user_role` should be `super_admin`

### **Step 2: Clear Browser Cache & Logout/Login**
1. **Logout** from the application
2. **Clear browser cache** (Ctrl+Shift+Delete)
3. **Login again** with the admin account
4. Check if admin redirect modal appears

### **Step 3: Check Browser Console**
1. Open **Developer Tools** (F12)
2. Go to **Console** tab
3. Look for any errors related to:
   - Profile loading
   - Authentication
   - Navigation

### **Step 4: Verify Profile Loading**
Add this temporary debug code to see what's happening:

**In Navigation.tsx, add this after line 18:**
```typescript
// DEBUG: Log profile data
console.log('Profile data:', profile);
console.log('Is Admin:', isAdmin);
```

**In Index.tsx, add this after line 32:**
```typescript
// DEBUG: Log user and profile
console.log('User:', user);
console.log('Profile:', profile);
console.log('Show Admin Redirect:', showAdminRedirect);
```

### **Step 5: Manual Navigation Test**
1. After logging in, manually navigate to `/admin` in the browser
2. Check if the admin dashboard loads properly
3. If it works, the issue is with the redirect logic

### **Step 6: Check Network Tab**
1. Open **Developer Tools** → **Network** tab
2. Login and watch for API calls
3. Look for profile/user data requests
4. Check if the profile data includes the updated `user_role`

## 🚨 Common Issues & Solutions

### **Issue 1: Profile Not Refreshing**
**Solution:** The profile hook might be caching old data
```typescript
// Force refresh profile data
const { profile, refetch } = useProfile();
useEffect(() => {
  if (user) {
    refetch?.();
  }
}, [user]);
```

### **Issue 2: RLS Policy Blocking**
**Solution:** Check if Row Level Security is preventing profile updates
```sql
-- Test if you can read the updated profile
SELECT * FROM profiles WHERE id = (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
);
```

### **Issue 3: Browser Caching**
**Solution:** Hard refresh or incognito mode
- **Hard Refresh:** Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)
- **Incognito Mode:** Test in private browsing

### **Issue 4: Session Not Updated**
**Solution:** The auth session might need to be refreshed
```typescript
// In useAuth hook, add session refresh
const refreshSession = async () => {
  const { data, error } = await supabase.auth.refreshSession();
  if (error) console.error('Session refresh error:', error);
};
```

## 🎯 Quick Test

**Run this in browser console after login:**
```javascript
// Check if admin elements exist
const adminLink = document.querySelector('[href="/admin"]');
console.log('Admin link found:', adminLink);

// Check profile data in localStorage/sessionStorage
console.log('Local storage:', localStorage);
console.log('Session storage:', sessionStorage);
```

## 🔄 Expected Behavior After Fix

1. **Login** → User sees home page briefly
2. **Admin Redirect Modal** appears with 3-second countdown
3. **Auto-redirect** to `/admin` dashboard
4. **Navigation** shows shield icon with "Admin" link
5. **Mobile menu** shows "Administrator" badge

## 📞 If Still Not Working

1. **Check the browser console** for errors
2. **Verify the database** shows correct user_role
3. **Try incognito mode** to rule out caching
4. **Manually navigate** to `/admin` to test dashboard access
5. **Check network requests** to see if profile data is loading

The most likely issue is browser caching or the profile hook not refreshing after the database update. Try logout/login first! 🔄
