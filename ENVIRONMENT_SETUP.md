# Environment Variables Setup

This project uses environment variables for configuration. Follow these steps to set up your environment:

## 1. Create Environment File

Copy the example environment file and configure it with your values:

```bash
cp .env.example .env
```

## 2. Configure Environment Variables

Edit the `.env` file with your actual configuration values:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Environment
NODE_ENV=development

# App Configuration
VITE_APP_NAME=BomaHub
VITE_APP_VERSION=1.0.0
```

## 3. Important Notes

- **Never commit `.env` files to version control** - they contain sensitive information
- The `.env` file is already added to `.gitignore`
- Use `.env.example` as a template for team members
- All Vite environment variables must be prefixed with `VITE_` to be accessible in the browser

## 4. Environment Variables Used

### Required Variables
- `VITE_SUPABASE_URL`: Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous key

### Optional Variables
- `NODE_ENV`: Environment mode (development, production)
- `VITE_APP_NAME`: Application name
- `VITE_APP_VERSION`: Application version

## 5. Getting Supabase Credentials

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to Settings > API
4. Copy the "Project URL" and "anon public" key
5. Paste them into your `.env` file

## 6. Deployment

For production deployments, set these environment variables in your hosting platform:
- Vercel: Project Settings > Environment Variables
- Netlify: Site Settings > Environment Variables
- Railway: Project Settings > Variables
