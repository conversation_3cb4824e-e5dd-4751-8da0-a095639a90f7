-- ============================================================================
-- CREATE ADMIN USERS FOR VERIFICATION SYSTEM
-- ============================================================================
-- Run this after the verification-system-migration.sql to create admin users
-- ============================================================================

-- Method 1: Promote an existing user to admin
-- Replace '<EMAIL>' with the actual email of the user you want to make admin
UPDATE public.profiles
SET
  user_role = 'super_admin',
  verification_status = 'verified',
  trust_score = 5.0,
  admin_notes = 'System Administrator - Created on ' || NOW()::date
WHERE id = (
  SELECT id FROM auth.users
  WHERE email = '<EMAIL>'  -- Change this to your admin email
  LIMIT 1
);

-- Example: If you want to make multiple users admin
-- UPDATE public.profiles
-- SET user_role = 'admin', verification_status = 'verified', trust_score = 5.0
-- WHERE id IN (
--   SELECT id FROM auth.users
--   WHERE email IN ('<EMAIL>', '<EMAIL>')
-- );

-- Method 2: Create a new admin user profile (after they sign up through the app)
-- First, the user must sign up through your app's normal registration process
-- Then run this query with their actual user ID:

-- INSERT INTO public.profiles (
--   id, 
--   full_name, 
--   phone, 
--   user_role, 
--   verification_status, 
--   trust_score,
--   admin_notes
-- ) VALUES (
--   'user-uuid-from-auth-users-table',
--   'Admin Full Name',
--   '+************',
--   'super_admin',
--   'verified',
--   5.0,
--   'System Administrator'
-- ) ON CONFLICT (id) DO UPDATE SET 
--   user_role = 'super_admin',
--   verification_status = 'verified',
--   trust_score = 5.0;

-- Method 3: Check existing users and their roles
SELECT 
  au.email,
  p.full_name,
  p.user_role,
  p.verification_status,
  p.created_at
FROM auth.users au
LEFT JOIN public.profiles p ON au.id = p.id
ORDER BY p.created_at DESC;

-- Method 4: Create multiple admin levels
-- Super Admin (full access)
-- UPDATE public.profiles 
-- SET user_role = 'super_admin'
-- WHERE id = 'super-admin-user-id';

-- Regular Admin (verification access only)
-- UPDATE public.profiles 
-- SET user_role = 'admin'
-- WHERE id = 'admin-user-id';

-- ============================================================================
-- VERIFY ADMIN SETUP
-- ============================================================================

-- Check admin users
SELECT 
  au.email,
  p.full_name,
  p.user_role,
  p.verification_status,
  p.trust_score
FROM auth.users au
JOIN public.profiles p ON au.id = p.id
WHERE p.user_role IN ('admin', 'super_admin')
ORDER BY p.user_role DESC;
