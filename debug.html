<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BomaHub Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>BomaHub System Debug</h1>
    
    <div class="debug-section">
        <h2>JavaScript Console Tests</h2>
        <button onclick="testConsole()">Test Console</button>
        <button onclick="testLocalStorage()">Test Local Storage</button>
        <button onclick="testFetch()">Test Network</button>
        <div id="console-output"></div>
    </div>

    <div class="debug-section">
        <h2>DOM Manipulation Tests</h2>
        <button onclick="testDOM()">Test DOM</button>
        <div id="dom-test"></div>
    </div>

    <div class="debug-section">
        <h2>React App Diagnostics</h2>
        <button onclick="checkReactApp()">Check React App Status</button>
        <button onclick="checkSupabaseConfig()">Check Supabase Config</button>
        <button onclick="checkEnvironmentVars()">Check Environment Variables</button>
        <button onclick="simulateReactErrors()">Simulate Common React Errors</button>
        <div id="react-diagnostics"></div>
    </div>

    <div class="debug-section">
        <h2>Network & API Tests</h2>
        <button onclick="testViteDevServer()">Test Vite Dev Server</button>
        <button onclick="testStaticAssets()">Test Static Assets</button>
        <button onclick="testCORS()">Test CORS Issues</button>
        <div id="network-diagnostics"></div>
    </div>

    <div class="debug-section">
        <h2>Browser Console Error Monitor</h2>
        <button onclick="startErrorMonitoring()">Start Error Monitoring</button>
        <button onclick="clearErrorLog()">Clear Error Log</button>
        <div id="error-monitor"></div>
    </div>

    <div class="debug-section">
        <h2>Advanced Search Filters Simulation</h2>
        <div id="filter-container">
            <label>County:</label>
            <select id="county-select">
                <option value="">Select County</option>
                <option value="Nairobi">Nairobi</option>
                <option value="Mombasa">Mombasa</option>
                <option value="Kisumu">Kisumu</option>
                <option value="Nakuru">Nakuru</option>
            </select>
            
            <label>Location:</label>
            <input type="text" id="location-input" placeholder="Enter location">
            
            <label>Price Range:</label>
            <input type="range" id="price-range" min="0" max="500000" step="5000">
            <span id="price-display">0 - 500000</span>
            
            <button onclick="applyFilters()">Apply Filters</button>
        </div>
        <div id="filter-results"></div>
    </div>

    <script>
        function testConsole() {
            const output = document.getElementById('console-output');
            try {
                console.log('Console test successful');
                output.innerHTML = '<span class="success">✓ Console working</span>';
            } catch (error) {
                output.innerHTML = '<span class="error">✗ Console error: ' + error.message + '</span>';
            }
        }

        function testLocalStorage() {
            const output = document.getElementById('console-output');
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                output.innerHTML += '<br><span class="success">✓ Local Storage working</span>';
            } catch (error) {
                output.innerHTML += '<br><span class="error">✗ Local Storage error: ' + error.message + '</span>';
            }
        }

        function testFetch() {
            const output = document.getElementById('console-output');
            fetch('https://jsonplaceholder.typicode.com/posts/1')
                .then(response => response.json())
                .then(data => {
                    output.innerHTML += '<br><span class="success">✓ Network/Fetch working</span>';
                })
                .catch(error => {
                    output.innerHTML += '<br><span class="error">✗ Network error: ' + error.message + '</span>';
                });
        }

        function testDOM() {
            const output = document.getElementById('dom-test');
            try {
                const testDiv = document.createElement('div');
                testDiv.innerHTML = 'DOM manipulation test successful';
                testDiv.style.color = 'green';
                output.appendChild(testDiv);
                
                // Test event handling
                const button = document.createElement('button');
                button.innerHTML = 'Click me';
                button.onclick = function() {
                    alert('Event handling works!');
                };
                output.appendChild(button);
            } catch (error) {
                output.innerHTML = '<span class="error">✗ DOM error: ' + error.message + '</span>';
            }
        }

        function applyFilters() {
            const county = document.getElementById('county-select').value;
            const location = document.getElementById('location-input').value;
            const priceRange = document.getElementById('price-range').value;
            const results = document.getElementById('filter-results');
            
            try {
                // Simulate the filter logic from AdvancedSearchFilters.tsx
                const filters = {
                    county: county,
                    location: location,
                    maxRent: parseInt(priceRange)
                };
                
                // Mock property data
                const mockProperties = [
                    { id: 1, county: 'Nairobi', location: 'Westlands', rent: 50000 },
                    { id: 2, county: 'Mombasa', location: 'Nyali', rent: 35000 },
                    { id: 3, county: 'Nairobi', location: 'Kilimani', rent: 75000 }
                ];
                
                // Filter properties
                let filteredProperties = mockProperties;
                
                if (filters.county) {
                    filteredProperties = filteredProperties.filter(p => p.county === filters.county);
                }
                
                if (filters.location) {
                    filteredProperties = filteredProperties.filter(p => 
                        p.location.toLowerCase().includes(filters.location.toLowerCase())
                    );
                }
                
                if (filters.maxRent > 0) {
                    filteredProperties = filteredProperties.filter(p => p.rent <= filters.maxRent);
                }
                
                results.innerHTML = '<h4>Filter Results:</h4>' +
                    '<span class="success">Found ' + filteredProperties.length + ' properties</span>' +
                    '<pre>' + JSON.stringify(filteredProperties, null, 2) + '</pre>';
                    
            } catch (error) {
                results.innerHTML = '<span class="error">✗ Filter error: ' + error.message + '</span>';
            }
        }

        // Error monitoring
        let errorLog = [];
        let isMonitoring = false;

        function startErrorMonitoring() {
            if (isMonitoring) return;
            isMonitoring = true;
            
            const output = document.getElementById('error-monitor');
            output.innerHTML = '<span class="warning">Monitoring errors... Check browser console for real-time updates</span>';
            
            // Capture console errors
            const originalError = console.error;
            console.error = function(...args) {
                errorLog.push({
                    type: 'console.error',
                    message: args.join(' '),
                    timestamp: new Date().toISOString()
                });
                updateErrorDisplay();
                originalError.apply(console, args);
            };

            // Capture unhandled errors
            window.addEventListener('error', function(e) {
                errorLog.push({
                    type: 'window.error',
                    message: e.message,
                    filename: e.filename,
                    lineno: e.lineno,
                    colno: e.colno,
                    timestamp: new Date().toISOString()
                });
                updateErrorDisplay();
            });

            // Capture unhandled promise rejections
            window.addEventListener('unhandledrejection', function(e) {
                errorLog.push({
                    type: 'unhandledrejection',
                    message: e.reason?.message || e.reason,
                    timestamp: new Date().toISOString()
                });
                updateErrorDisplay();
            });
        }

        function updateErrorDisplay() {
            const output = document.getElementById('error-monitor');
            if (errorLog.length === 0) {
                output.innerHTML = '<span class="success">No errors detected</span>';
            } else {
                output.innerHTML = '<h4>Detected Errors:</h4>' +
                    errorLog.map(error => 
                        `<div class="error">[${error.timestamp}] ${error.type}: ${error.message}</div>`
                    ).join('');
            }
        }

        function clearErrorLog() {
            errorLog = [];
            updateErrorDisplay();
        }

        function checkReactApp() {
            const output = document.getElementById('react-diagnostics');
            let diagnostics = [];

            try {
                // Check if React is loaded
                if (typeof React !== 'undefined') {
                    diagnostics.push('<span class="success">✓ React library detected</span>');
                } else {
                    diagnostics.push('<span class="error">✗ React library not found</span>');
                }

                // Check if ReactDOM is loaded
                if (typeof ReactDOM !== 'undefined') {
                    diagnostics.push('<span class="success">✓ ReactDOM library detected</span>');
                } else {
                    diagnostics.push('<span class="error">✗ ReactDOM library not found</span>');
                }

                // Check root element
                const rootElement = document.getElementById('root');
                if (rootElement) {
                    diagnostics.push('<span class="success">✓ Root element found</span>');
                    if (rootElement.innerHTML.trim()) {
                        diagnostics.push('<span class="success">✓ Root element has content</span>');
                    } else {
                        diagnostics.push('<span class="error">✗ Root element is empty</span>');
                    }
                } else {
                    diagnostics.push('<span class="error">✗ Root element not found</span>');
                }

                // Check for common React errors
                const scripts = document.querySelectorAll('script[src*="main"]');
                if (scripts.length > 0) {
                    diagnostics.push('<span class="success">✓ Main script found</span>');
                } else {
                    diagnostics.push('<span class="warning">⚠ Main script not found - may indicate build issues</span>');
                }

                output.innerHTML = diagnostics.join('<br>');

            } catch (error) {
                output.innerHTML = '<span class="error">✗ React diagnostics error: ' + error.message + '</span>';
            }
        }

        function checkSupabaseConfig() {
            const output = document.getElementById('react-diagnostics');
            let diagnostics = [];

            try {
                // This will only work if we're running in the actual app context
                // For now, we'll simulate the checks
                diagnostics.push('<span class="warning">⚠ Supabase config check requires app context</span>');
                diagnostics.push('<span class="info">Check browser console for VITE_SUPABASE_URL errors</span>');
                diagnostics.push('<span class="info">Check browser console for VITE_SUPABASE_ANON_KEY errors</span>');
                
                output.innerHTML += '<br><h4>Supabase Config:</h4>' + diagnostics.join('<br>');
            } catch (error) {
                output.innerHTML += '<br><span class="error">✗ Supabase config error: ' + error.message + '</span>';
            }
        }

        function checkEnvironmentVars() {
            const output = document.getElementById('react-diagnostics');
            let diagnostics = [];

            try {
                // Check if we can access meta env (only works in dev mode)
                diagnostics.push('<span class="warning">⚠ Environment variables check requires Vite dev context</span>');
                diagnostics.push('<span class="info">Expected vars: VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY</span>');
                diagnostics.push('<span class="info">Check .env file exists in project root</span>');
                
                output.innerHTML += '<br><h4>Environment Variables:</h4>' + diagnostics.join('<br>');
            } catch (error) {
                output.innerHTML += '<br><span class="error">✗ Environment check error: ' + error.message + '</span>';
            }
        }

        function simulateReactErrors() {
            const output = document.getElementById('react-diagnostics');
            let diagnostics = [];

            // Common React error scenarios
            const commonErrors = [
                'Missing environment variables (VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY)',
                'Supabase client initialization failure',
                'Router configuration issues',
                'Component import/export errors',
                'TypeScript compilation errors',
                'CSS/Tailwind loading issues',
                'Service Worker conflicts',
                'Local storage access issues'
            ];

            diagnostics.push('<h4>Common Blank Page Causes:</h4>');
            commonErrors.forEach(error => {
                diagnostics.push(`<span class="warning">⚠ ${error}</span>`);
            });

            output.innerHTML += '<br>' + diagnostics.join('<br>');
        }

        function testViteDevServer() {
            const output = document.getElementById('network-diagnostics');
            
            // Test if Vite dev server is accessible
            const testUrls = [
                'http://localhost:8080/',
                'http://*************:8080/',
                'http://**************:8080/'
            ];

            testUrls.forEach(url => {
                fetch(url, { mode: 'no-cors' })
                    .then(() => {
                        output.innerHTML += `<br><span class="success">✓ ${url} accessible</span>`;
                    })
                    .catch(error => {
                        output.innerHTML += `<br><span class="error">✗ ${url} not accessible: ${error.message}</span>`;
                    });
            });
        }

        function testStaticAssets() {
            const output = document.getElementById('network-diagnostics');
            
            // Test common static assets
            const assets = [
                '/house-favicon.svg',
                '/manifest.json',
                '/sw.js'
            ];

            assets.forEach(asset => {
                fetch(asset)
                    .then(response => {
                        if (response.ok) {
                            output.innerHTML += `<br><span class="success">✓ ${asset} loaded</span>`;
                        } else {
                            output.innerHTML += `<br><span class="error">✗ ${asset} failed to load (${response.status})</span>`;
                        }
                    })
                    .catch(error => {
                        output.innerHTML += `<br><span class="error">✗ ${asset} error: ${error.message}</span>`;
                    });
            });
        }

        function testCORS() {
            const output = document.getElementById('network-diagnostics');
            
            // Test CORS with a simple request
            fetch('https://api.github.com/repos/Hassan1910/bomahub', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (response.ok) {
                    output.innerHTML += '<br><span class="success">✓ External API access working</span>';
                } else {
                    output.innerHTML += '<br><span class="warning">⚠ External API returned: ' + response.status + '</span>';
                }
            })
            .catch(error => {
                output.innerHTML += '<br><span class="error">✗ CORS/Network error: ' + error.message + '</span>';
            });
        }

        // Update price display
        document.getElementById('price-range').addEventListener('input', function() {
            document.getElementById('price-display').textContent = '0 - ' + this.value;
        });

        // Auto-run tests on page load
        window.onload = function() {
            startErrorMonitoring();
            testConsole();
            testLocalStorage();
            testFetch();
            testDOM();
            checkReactApp();
            checkSupabaseConfig();
            checkEnvironmentVars();
        };
    </script>
</body>
</html>
