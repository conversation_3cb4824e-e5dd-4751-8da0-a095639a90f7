-- Fix missing RLS policy for admin profile updates
-- This script adds the missing policy that allows admins to update user profiles for verification

-- Add policy for admins to update all profiles (needed for verification status updates)
DROP POLICY IF EXISTS "Admins can update all profiles" ON public.profiles;

CREATE POLICY "Ad<PERSON> can update all profiles" ON public.profiles
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND user_role IN ('admin', 'super_admin')
    )
  );

-- Verify the policy was created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'profiles'
AND policyname = '<PERSON><PERSON> can update all profiles';
