-- ============================================================================
-- HOTFIX: Fix Audit Trigger Function
-- ============================================================================
-- This fixes the "record NEW has no field user_id" error
-- Run this if you already ran the verification migration and got the error
-- ============================================================================

-- Drop existing triggers first
DROP TRIGGER IF EXISTS log_verification_document_changes ON public.verification_documents;
DROP TRIGGER IF EXISTS log_profile_verification_changes ON public.profiles;

-- Drop and recreate the function with proper table handling
DROP FUNCTION IF EXISTS public.log_verification_action();

-- Create the fixed function
CREATE OR REPLACE FUNCTION public.log_verification_action()
RETURNS TRIGGER AS $$
DECLARE
  target_user_id UUID;
BEGIN
  -- Determine the user_id based on the table structure
  IF TG_TABLE_NAME = 'profiles' THEN
    target_user_id := COALESCE(NEW.id, OLD.id);
  ELSE
    target_user_id := COALESCE(NEW.user_id, OLD.user_id);
  END IF;

  INSERT INTO public.verification_audit_log (
    user_id,
    action,
    details,
    performed_by
  ) VALUES (
    target_user_id,
    TG_OP || '_' || TG_TABLE_NAME,
    jsonb_build_object(
      'old', to_jsonb(OLD),
      'new', to_jsonb(NEW)
    ),
    auth.uid()
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the triggers
CREATE TRIGGER log_verification_document_changes
  AFTER INSERT OR UPDATE OR DELETE ON public.verification_documents
  FOR EACH ROW EXECUTE PROCEDURE public.log_verification_action();

CREATE TRIGGER log_profile_verification_changes
  AFTER UPDATE OF verification_status ON public.profiles
  FOR EACH ROW EXECUTE PROCEDURE public.log_verification_action();

-- Test the fix by checking if the function exists
SELECT 
  proname as function_name,
  prosrc as function_body
FROM pg_proc 
WHERE proname = 'log_verification_action';

-- ============================================================================
-- HOTFIX COMPLETE
-- ============================================================================
-- The audit trigger function has been fixed to handle different table structures
-- - profiles table uses 'id' field
-- - other tables use 'user_id' field
-- ============================================================================
