-- Migration to fix existing document URLs in the database
-- This script updates document URLs to store file paths instead of full URLs
-- since we're now using signed URLs for private bucket access

-- First, let's see what we have
SELECT 
    id,
    document_type,
    document_name,
    document_url,
    CASE 
        WHEN document_url LIKE '%/storage/v1/object/public/verification-documents/%' THEN 
            SUBSTRING(document_url FROM '/storage/v1/object/public/verification-documents/(.+)')
        WHEN document_url LIKE '%/storage/v1/object/sign/verification-documents/%' THEN 
            SUBSTRING(document_url FROM '/storage/v1/object/sign/verification-documents/([^?]+)')
        WHEN document_url LIKE 'verification-documents/%' THEN 
            SUBSTRING(document_url FROM 'verification-documents/(.+)')
        ELSE document_url
    END as extracted_path
FROM verification_documents 
WHERE document_url IS NOT NULL
ORDER BY created_at DESC;

-- Update document URLs to store just the file path
UPDATE verification_documents 
SET document_url = CASE 
    -- If it's a full public URL, extract the file path
    WHEN document_url LIKE '%/storage/v1/object/public/verification-documents/%' THEN 
        SUBSTRING(document_url FROM '/storage/v1/object/public/verification-documents/(.+)')
    -- If it's a signed URL, extract the file path (remove query params)
    WHEN document_url LIKE '%/storage/v1/object/sign/verification-documents/%' THEN 
        SUBSTRING(document_url FROM '/storage/v1/object/sign/verification-documents/([^?]+)')
    -- If it already has the bucket prefix, remove it
    WHEN document_url LIKE 'verification-documents/%' THEN 
        SUBSTRING(document_url FROM 'verification-documents/(.+)')
    -- Otherwise, assume it's already a clean file path
    ELSE document_url
END,
updated_at = NOW()
WHERE document_url IS NOT NULL
AND (
    document_url LIKE '%/storage/v1/object/%' 
    OR document_url LIKE 'verification-documents/%'
);

-- Verify the update
SELECT 
    COUNT(*) as total_documents,
    COUNT(CASE WHEN document_url LIKE '%/storage/v1/object/%' THEN 1 END) as urls_with_full_path,
    COUNT(CASE WHEN document_url NOT LIKE '%/storage/v1/object/%' AND document_url NOT LIKE 'http%' THEN 1 END) as clean_file_paths
FROM verification_documents 
WHERE document_url IS NOT NULL;

-- Show sample of updated records
SELECT 
    id,
    document_type,
    document_name,
    document_url,
    updated_at
FROM verification_documents 
WHERE document_url IS NOT NULL
ORDER BY updated_at DESC
LIMIT 10;
