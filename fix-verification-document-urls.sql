-- Fix verification document URLs that might be causing display issues
-- This script regenerates public URLs for verification documents

-- First, let's check for documents with potentially broken URLs
SELECT 
    id,
    user_id,
    document_type,
    document_name,
    document_url,
    created_at
FROM verification_documents
WHERE document_url IS NOT NULL
ORDER BY created_at DESC;

-- Optional: Update document URLs to ensure they follow the correct format
-- This would typically be done through the application, but can be done here if needed

-- Add a column to track URL regeneration (optional)
-- ALTER TABLE verification_documents ADD COLUMN url_regenerated_at TIMESTAMP WITH TIME ZONE;

-- Example of how to fix URLs programmatically (uncomment if needed):
/*
UPDATE verification_documents 
SET document_url = CASE 
    WHEN document_url LIKE '%/storage/v1/object/public/verification-documents/%' 
    THEN document_url 
    ELSE CONCAT(
        'https://your-project.supabase.co/storage/v1/object/public/verification-documents/',
        REPLACE(document_url, 'verification-documents/', '')
    )
    END,
    url_regenerated_at = NOW()
WHERE document_url IS NOT NULL;
*/

-- Add indexes for better performance on admin queries
CREATE INDEX IF NOT EXISTS idx_verification_documents_user_verification 
ON verification_documents(user_id, verification_status);

CREATE INDEX IF NOT EXISTS idx_verification_documents_type_status 
ON verification_documents(document_type, verification_status);

-- Add a trigger to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_verification_documents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS trigger_verification_documents_updated_at ON verification_documents;
CREATE TRIGGER trigger_verification_documents_updated_at
    BEFORE UPDATE ON verification_documents
    FOR EACH ROW
    EXECUTE FUNCTION update_verification_documents_updated_at();

-- Ensure storage policies are correctly set up for verification documents
-- (This should already be done, but included for completeness)

-- Policy for users to view their own documents
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'storage' 
        AND tablename = 'objects' 
        AND policyname = 'Users can view their own verification documents'
    ) THEN
        EXECUTE 'CREATE POLICY "Users can view their own verification documents" ON storage.objects
            FOR SELECT USING (
                bucket_id = ''verification-documents''
                AND auth.uid()::text = (storage.foldername(name))[1]
            )';
    END IF;
END $$;

-- Policy for admins to view all verification documents
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'storage' 
        AND tablename = 'objects' 
        AND policyname = 'Admins can view all verification documents'
    ) THEN
        EXECUTE 'CREATE POLICY "Admins can view all verification documents" ON storage.objects
            FOR SELECT USING (
                bucket_id = ''verification-documents''
                AND EXISTS (
                    SELECT 1 FROM profiles
                    WHERE id = auth.uid()
                    AND user_role IN (''admin'', ''super_admin'')
                )
            )';
    END IF;
END $$;
