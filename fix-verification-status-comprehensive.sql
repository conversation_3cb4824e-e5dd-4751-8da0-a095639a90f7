-- Comprehensive fix for verification status updates
-- This script addresses the issue where verification status remains pending after admin approval

-- ============================================================================
-- STEP 1: ADD MISSING RLS POLICY FOR ADMIN PROFILE UPDATES
-- ============================================================================

-- Add policy for admins to update all profiles (needed for verification status updates)
DROP POLICY IF EXISTS "Admins can update all profiles" ON public.profiles;

CREATE POLICY "Admins can update all profiles" ON public.profiles
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND user_role IN ('admin', 'super_admin')
    )
  );

-- ============================================================================
-- STEP 2: VERIFICATION STATUS DEBUGGING QUERIES
-- ============================================================================

-- Check current verification statuses
SELECT 
    id,
    full_name,
    user_role,
    verification_status,
    verification_submitted_at,
    verification_completed_at,
    verified_by,
    trust_score,
    admin_notes,
    created_at,
    updated_at
FROM profiles 
WHERE user_role = 'landlord'
AND verification_status IS NOT NULL
ORDER BY verification_submitted_at DESC NULLS LAST;

-- Check verification documents status
SELECT 
    vd.id,
    vd.user_id,
    p.full_name,
    vd.document_type,
    vd.verification_status as doc_status,
    p.verification_status as profile_status,
    vd.created_at as doc_created,
    vd.reviewed_at,
    vd.reviewed_by
FROM verification_documents vd
JOIN profiles p ON vd.user_id = p.id
WHERE p.user_role = 'landlord'
ORDER BY vd.created_at DESC;

-- Check for pending verifications with completed documents
SELECT 
    p.id,
    p.full_name,
    p.verification_status as profile_status,
    COUNT(vd.id) as total_docs,
    COUNT(CASE WHEN vd.verification_status = 'approved' THEN 1 END) as approved_docs,
    COUNT(CASE WHEN vd.verification_status = 'rejected' THEN 1 END) as rejected_docs,
    COUNT(CASE WHEN vd.verification_status = 'pending' THEN 1 END) as pending_docs
FROM profiles p
LEFT JOIN verification_documents vd ON p.id = vd.user_id
WHERE p.user_role = 'landlord'
AND p.verification_status IN ('pending', 'under_review')
GROUP BY p.id, p.full_name, p.verification_status
HAVING COUNT(vd.id) > 0;

-- ============================================================================
-- STEP 3: VERIFY RLS POLICIES
-- ============================================================================

-- Check all policies on profiles table
SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'profiles'
ORDER BY policyname;

-- ============================================================================
-- STEP 4: CREATE NOTIFICATION FUNCTION FOR VERIFICATION UPDATES
-- ============================================================================

-- Create function to notify when verification status changes
CREATE OR REPLACE FUNCTION notify_verification_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only notify for verification status changes
    IF OLD.verification_status IS DISTINCT FROM NEW.verification_status THEN
        PERFORM pg_notify(
            'verification_status_changed',
            json_build_object(
                'user_id', NEW.id,
                'old_status', OLD.verification_status,
                'new_status', NEW.verification_status,
                'verified_by', NEW.verified_by,
                'timestamp', NEW.updated_at
            )::text
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_verification_status_change ON profiles;

-- Create trigger for verification status changes
CREATE TRIGGER trigger_verification_status_change
    AFTER UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION notify_verification_status_change();

-- ============================================================================
-- STEP 5: VERIFICATION STATUS CONSISTENCY CHECK
-- ============================================================================

-- Function to check and fix verification status consistency
CREATE OR REPLACE FUNCTION check_verification_consistency()
RETURNS TABLE(
    user_id UUID,
    full_name TEXT,
    current_status TEXT,
    should_be_status TEXT,
    action_needed TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id as user_id,
        p.full_name,
        p.verification_status as current_status,
        CASE 
            WHEN COUNT(vd.id) = 0 THEN 'pending'
            WHEN COUNT(CASE WHEN vd.verification_status = 'approved' THEN 1 END) >= 1 
                AND COUNT(CASE WHEN vd.verification_status = 'rejected' THEN 1 END) = 0 THEN 'verified'
            WHEN COUNT(CASE WHEN vd.verification_status = 'rejected' THEN 1 END) > 0 THEN 'rejected'
            ELSE 'under_review'
        END as should_be_status,
        CASE 
            WHEN p.verification_status != (
                CASE 
                    WHEN COUNT(vd.id) = 0 THEN 'pending'
                    WHEN COUNT(CASE WHEN vd.verification_status = 'approved' THEN 1 END) >= 1 
                        AND COUNT(CASE WHEN vd.verification_status = 'rejected' THEN 1 END) = 0 THEN 'verified'
                    WHEN COUNT(CASE WHEN vd.verification_status = 'rejected' THEN 1 END) > 0 THEN 'rejected'
                    ELSE 'under_review'
                END
            ) THEN 'UPDATE NEEDED'
            ELSE 'OK'
        END as action_needed
    FROM profiles p
    LEFT JOIN verification_documents vd ON p.id = vd.user_id
    WHERE p.user_role = 'landlord'
    GROUP BY p.id, p.full_name, p.verification_status;
END;
$$ LANGUAGE plpgsql;

-- Run the consistency check
SELECT * FROM check_verification_consistency()
WHERE action_needed = 'UPDATE NEEDED';

-- ============================================================================
-- STEP 6: VERIFICATION STATUS
-- ============================================================================

-- Show final status
SELECT 'RLS Policy Added for Admin Profile Updates' as status
UNION ALL
SELECT 'Verification Status Debugging Queries Available' as status
UNION ALL
SELECT 'Notification Function Created for Real-time Updates' as status
UNION ALL
SELECT 'Consistency Check Function Available' as status;
