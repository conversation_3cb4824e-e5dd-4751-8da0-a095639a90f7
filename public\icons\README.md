# App Icons

This directory should contain the following PWA icons:

## Required Icons:
- `icon-152.png` (152x152) - Apple Touch Icon for iPad
- `icon-167.png` (167x167) - Apple Touch Icon for iPad Pro 
- `icon-180.png` (180x180) - Apple Touch Icon for iPhone
- `icon-192.png` (192x192) - Android Chrome icon
- `icon-512.png` (512x512) - Android Chrome large icon
- `icon-192-maskable.png` (192x192) - Maskable icon for adaptive icons
- `icon-512-maskable.png` (512x512) - Large maskable icon
- `icon-150.png` (150x150) - Microsoft tile icon

## How to Generate Icons:
1. Start with your existing `house-favicon.svg` 
2. Use an online PWA icon generator like:
   - https://www.pwabuilder.com/imageGenerator
   - https://realfavicongenerator.net/
   - https://favicon.io/favicon-converter/

3. Upload your SVG and download the generated icon pack
4. Place the icons in this directory with the names listed above

## Notes:
- Maskable icons should have padding so they work with adaptive icon shapes
- All icons should be square (1:1 aspect ratio)
- Use PNG format for better compatibility
- Icons should be clear and recognizable at small sizes
