import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/components/AuthProvider";
import { PWAInstallPrompt } from "@/components/PWAInstallPrompt";
import { ThemeProvider } from "@/components/ThemeProvider";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { PerformanceDebug } from "@/components/debug/PerformanceDebug";
import { PerformanceDashboard } from "@/components/debug/PerformanceDashboard";
import { lazy, Suspense } from "react";
import { LoadingSkeleton } from "@/components/ui/loading-skeleton";

// Route-based code splitting - lazy load all pages
const Index = lazy(() => import("./pages/Index"));
const Auth = lazy(() => import("./pages/Auth"));
const NotFound = lazy(() => import("./pages/NotFound"));
const AddProperty = lazy(() => import("./pages/AddProperty"));
const EditProperty = lazy(() => import("./pages/EditProperty"));
const PropertyDetail = lazy(() => import("./pages/PropertyDetail"));
const MyProperties = lazy(() => import("./pages/MyProperties"));
const Profile = lazy(() => import("./pages/Profile"));
const ProfileDebug = lazy(() => import("./pages/ProfileDebug"));
const AdminDashboard = lazy(() => import("./pages/AdminDashboard"));
const LandlordVerification = lazy(() => import("./pages/LandlordVerification"));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <PWAInstallPrompt />
            <PerformanceDebug />
            <PerformanceDashboard />
            <BrowserRouter>
              <Suspense fallback={
                <div className="min-h-screen bg-background flex items-center justify-center">
                  <div className="max-w-md w-full p-6">
                    <LoadingSkeleton showAvatar lines={5} />
                  </div>
                </div>
              }>
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/auth" element={<Auth />} />
                  <Route path="/add-property" element={<AddProperty />} />
                  <Route path="/edit-property/:id" element={<EditProperty />} />
                  <Route path="/property/:id" element={<PropertyDetail />} />
                  <Route path="/my-properties" element={<MyProperties />} />
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/profile/debug" element={<ProfileDebug />} />
                  <Route path="/admin" element={<AdminDashboard />} />
                  <Route path="/verification" element={<LandlordVerification />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Suspense>
            </BrowserRouter>
          </TooltipProvider>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
