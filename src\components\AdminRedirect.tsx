import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, ArrowRight, Home } from 'lucide-react';

interface AdminRedirectProps {
  userRole: string;
  userName?: string;
}

export const AdminRedirect: React.FC<AdminRedirectProps> = ({ userRole, userName }) => {
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          navigate('/admin');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [navigate]);

  const handleGoToAdmin = () => {
    navigate('/admin');
  };

  const handleStayOnHome = () => {
    navigate('/', { replace: true });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="p-8 text-center space-y-6">
          <div className="flex justify-center">
            <div className="p-4 bg-blue-100 rounded-full">
              <Shield className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome, Administrator!
            </h2>
            <p className="text-muted-foreground">
              {userName && `Hello ${userName}, `}
              You have {userRole.replace('_', ' ')} privileges.
            </p>
          </div>

          <div className="p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800 mb-2">
              Redirecting to Admin Dashboard in:
            </p>
            <div className="text-3xl font-bold text-blue-600">
              {countdown}
            </div>
          </div>

          <div className="space-y-3">
            <Button 
              onClick={handleGoToAdmin}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              <Shield className="h-4 w-4 mr-2" />
              Go to Admin Dashboard
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
            
            <Button 
              variant="outline"
              onClick={handleStayOnHome}
              className="w-full"
            >
              <Home className="h-4 w-4 mr-2" />
              Stay on Home Page
            </Button>
          </div>

          <p className="text-xs text-muted-foreground">
            You can always access the admin dashboard from the navigation menu
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
