import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { X, Filter, MapPin, DollarSign } from 'lucide-react';
import { SearchFilters } from '@/types/property';

interface AdvancedSearchFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onClearFilters: () => void;
}

const KENYAN_COUNTIES = [
  'Nairobi', 'Mombasa', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Eldor<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
];

const POPULAR_AREAS = {
  'Nairobi': ['Westlands', 'Karen', 'Kilimani', 'Lavington', 'Kileleshwa', 'Emba<PERSON>i', 'Kasarani', 'Ruaka'],
  'Mombasa': ['Nyali', 'Bamburi', 'Diani', 'Shanzu', 'Tudor', 'Changamwe'],
  'Kisumu': ['Milimani', 'Mamboleo', 'Nyamasaria', 'Tom Mboya', 'Central'],
  'Nakuru': ['Milimani', 'Section 58', 'Kiamunyi', 'Free Area', 'Rhonda']
};

const AMENITIES = [
  'Parking', 'Security', 'Swimming Pool', 'Gym', 'Garden', 'Balcony',
  'Air Conditioning', 'Furnished', 'Internet/WiFi', 'Generator', 'Water Tank',
  'CCTV', 'Elevator', 'Playground', 'Shopping Center Nearby', 'School Nearby'
];

export const AdvancedSearchFilters: React.FC<AdvancedSearchFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [priceRange, setPriceRange] = useState([filters.minRent || 0, filters.maxRent || 200000]);

  const updateFilter = (key: keyof SearchFilters, value: SearchFilters[keyof SearchFilters]) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const toggleAmenity = (amenity: string) => {
    const currentAmenities = filters.amenities || [];
    const newAmenities = currentAmenities.includes(amenity)
      ? currentAmenities.filter(a => a !== amenity)
      : [...currentAmenities, amenity];
    updateFilter('amenities', newAmenities);
  };

  const handlePriceChange = (values: number[]) => {
    setPriceRange(values);
    updateFilter('minRent', values[0]);
    updateFilter('maxRent', values[1]);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.location) count++;
    if (filters.county) count++;
    if (filters.property_type) count++;
    if (filters.minRent || filters.maxRent) count++;
    if (filters.bedrooms) count++;
    if (filters.amenities?.length) count += filters.amenities.length;
    return count;
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Filter className="h-5 w-5" />
            Search Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFiltersCount()} active
              </Badge>
            )}
          </CardTitle>
          <div className="flex gap-2">
            {getActiveFiltersCount() > 0 && (
              <Button variant="outline" size="sm" onClick={onClearFilters}>
                Clear All
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Show Less' : 'More Filters'}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Basic Filters - Always Visible */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              County
            </Label>
            <Select value={filters.county || 'all'} onValueChange={(value) => updateFilter('county', value === 'all' ? undefined : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select county" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Counties</SelectItem>
                {KENYAN_COUNTIES.map(county => (
                  <SelectItem key={county} value={county}>{county}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Specific Area/Location</Label>
            <Input
              placeholder="e.g., Westlands, Kilifi"
              value={filters.location || ''}
              onChange={(e) => updateFilter('location', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label>Bedrooms</Label>
            <Select value={filters.bedrooms?.toString() || 'any'} onValueChange={(value) => updateFilter('bedrooms', value === 'any' ? undefined : parseInt(value))}>
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any</SelectItem>
                <SelectItem value="1">1 Bedroom</SelectItem>
                <SelectItem value="2">2 Bedrooms</SelectItem>
                <SelectItem value="3">3 Bedrooms</SelectItem>
                <SelectItem value="4">4+ Bedrooms</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Property Type</Label>
            <Select value={filters.property_type || 'any'} onValueChange={(value) => updateFilter('property_type', value === 'any' ? undefined : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Any type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any Type</SelectItem>
                <SelectItem value="apartment">Apartment</SelectItem>
                <SelectItem value="house">House</SelectItem>
                <SelectItem value="studio">Studio</SelectItem>
                <SelectItem value="bedsitter">Bedsitter</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Advanced Filters - Expandable */}
        {isExpanded && (
          <div className="space-y-6 pt-4 border-t">
            {/* Price Range */}
            <div className="space-y-3">
              <Label className="flex items-center gap-1">
                <DollarSign className="h-4 w-4" />
                Monthly Rent Range (KSH)
              </Label>
              <div className="px-2">
                <Slider
                  value={priceRange}
                  onValueChange={handlePriceChange}
                  max={500000}
                  min={0}
                  step={5000}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-1">
                  <span>KSH {priceRange[0].toLocaleString()}</span>
                  <span>KSH {priceRange[1].toLocaleString()}</span>
                </div>
              </div>
            </div>

            {/* Popular Areas for Selected County */}
            {filters.county && POPULAR_AREAS[filters.county as keyof typeof POPULAR_AREAS] && (
              <div className="space-y-3">
                <Label>Popular Areas in {filters.county}</Label>
                <div className="flex flex-wrap gap-2">
                  {POPULAR_AREAS[filters.county as keyof typeof POPULAR_AREAS].map(area => (
                    <Button
                      key={area}
                      variant={filters.location === area ? "default" : "outline"}
                      size="sm"
                      onClick={() => updateFilter('location', filters.location === area ? '' : area)}
                    >
                      {area}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Amenities */}
            <div className="space-y-3">
              <Label>Amenities & Features</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {AMENITIES.map(amenity => (
                  <div key={amenity} className="flex items-center space-x-2">
                    <Checkbox
                      id={amenity}
                      checked={filters.amenities?.includes(amenity) || false}
                      onCheckedChange={() => toggleAmenity(amenity)}
                    />
                    <Label htmlFor={amenity} className="text-sm">{amenity}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Filters */}
            <div className="space-y-3">
              <Label>Quick Filters</Label>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={filters.featured ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilter('featured', !filters.featured)}
                >
                  Featured Properties
                </Button>
                <Button
                  variant={filters.available ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilter('available', !filters.available)}
                >
                  Available Now
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters Display */}
        {getActiveFiltersCount() > 0 && (
          <div className="flex flex-wrap gap-2 pt-3 border-t">
            {filters.county && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {filters.county}
                <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter('county', '')} />
              </Badge>
            )}
            {filters.location && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {filters.location}
                <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter('location', '')} />
              </Badge>
            )}
            {filters.property_type && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {filters.property_type}
                <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter('property_type', undefined)} />
              </Badge>
            )}
            {filters.bedrooms && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {filters.bedrooms} bed{filters.bedrooms > 1 ? 's' : ''}
                <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter('bedrooms', undefined)} />
              </Badge>
            )}
            {filters.amenities?.map(amenity => (
              <Badge key={amenity} variant="secondary" className="flex items-center gap-1">
                {amenity}
                <X className="h-3 w-3 cursor-pointer" onClick={() => toggleAmenity(amenity)} />
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
