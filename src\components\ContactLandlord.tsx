import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Phone, Mail, MessageCircle, Calendar, Copy, Check } from 'lucide-react';
import { PropertyWithOwner } from '@/types/property';
import { useToast } from '@/hooks/use-toast';

interface ContactLandlordProps {
  property: PropertyWithOwner;
}

export const ContactLandlord: React.FC<ContactLandlordProps> = ({ property }) => {
  const [inquiryType, setInquiryType] = useState('general');
  const [message, setMessage] = useState('');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [preferredContact, setPreferredContact] = useState('phone');
  const [showForm, setShowForm] = useState(false);
  const [copiedPhone, setCopiedPhone] = useState(false);
  const { toast } = useToast();

  const landlordPhone = property.profiles?.phone || "+254 700 000 000";
  const landlordName = property.profiles?.full_name || "Property Owner";

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedPhone(true);
      setTimeout(() => setCopiedPhone(false), 2000);
      toast({
        title: "Phone number copied!",
        description: "You can now paste it in your dialer",
      });
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const getPrefilledMessage = () => {
    const messages = {
      viewing: `Hi ${landlordName},\n\nI'm interested in viewing your property "${property.title}" located at ${property.location}.\n\nI found this listing on BomaHub. Could we schedule a viewing at your convenience?\n\nThank you!`,
      pricing: `Hi ${landlordName},\n\nI'm interested in your property "${property.title}" at ${property.location}.\n\nI found this listing on BomaHub. Could you provide more details about:\n- Final rent amount\n- Security deposit required\n- Any additional fees\n- Move-in requirements\n\nThank you!`,
      availability: `Hi ${landlordName},\n\nI'm interested in your property "${property.title}" at ${property.location}.\n\nI found this listing on BomaHub. Is this property still available? If so, when would be the earliest possible move-in date?\n\nThank you!`,
      general: `Hi ${landlordName},\n\nI'm interested in your property "${property.title}" located at ${property.location}.\n\nI found this listing on BomaHub. Could you please provide more details?\n\nThank you!`
    };
    return messages[inquiryType as keyof typeof messages];
  };

  const handleWhatsAppClick = () => {
    const whatsappNumber = landlordPhone.replace(/\D/g, ''); // Remove non-digits
    const message = encodeURIComponent(getPrefilledMessage());
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleEmailClick = () => {
    const subject = encodeURIComponent(`Inquiry about: ${property.title}`);
    const body = encodeURIComponent(getPrefilledMessage());
    window.open(`mailto:?subject=${subject}&body=${body}`, '_self');
  };

  const handleCallClick = () => {
    window.open(`tel:${landlordPhone}`, '_self');
  };

  const handleSendInquiry = () => {
    // Here you would typically send the inquiry to your backend
    // For now, we'll just compose an email
    const fullMessage = `
Name: ${name}
Email: ${email}
Phone: ${phone}
Preferred Contact: ${preferredContact}
Inquiry Type: ${inquiryType}

Message:
${message}

Property: ${property.title}
Location: ${property.location}
Rent: KSH ${property.rent.toLocaleString()}
    `;

    const subject = encodeURIComponent(`Property Inquiry: ${property.title}`);
    const body = encodeURIComponent(fullMessage);
    window.open(`mailto:?subject=${subject}&body=${body}`, '_self');

    toast({
      title: "Inquiry sent!",
      description: "Your default email client has been opened with the inquiry details.",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Contact Property Owner</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Landlord Info */}
        <div className="bg-muted/30 rounded-lg p-4">
          <h4 className="font-semibold text-foreground">{landlordName}</h4>
          <p className="text-sm text-muted-foreground">Property Owner</p>
          <div className="mt-2 flex items-center gap-2">
            <span className="text-sm">{landlordPhone}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(landlordPhone)}
              className="h-6 w-6 p-0"
            >
              {copiedPhone ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {/* Quick Contact Buttons */}
        <div className="grid grid-cols-1 gap-3">
          <Button 
            className="w-full justify-start" 
            onClick={handleCallClick}
          >
            <Phone className="h-4 w-4 mr-2" />
            Call Now
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full justify-start bg-green-50 hover:bg-green-100 border-green-200"
            onClick={handleWhatsAppClick}
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            WhatsApp Message
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full justify-start"
            onClick={handleEmailClick}
          >
            <Mail className="h-4 w-4 mr-2" />
            Send Email
          </Button>

          <Button 
            variant="ghost" 
            className="w-full justify-start"
            onClick={() => setShowForm(!showForm)}
          >
            <Calendar className="h-4 w-4 mr-2" />
            {showForm ? 'Hide' : 'Send'} Detailed Inquiry
          </Button>
        </div>

        {/* Detailed Inquiry Form */}
        {showForm && (
          <div className="space-y-4 pt-4 border-t">
            <div className="space-y-2">
              <Label>Inquiry Type</Label>
              <Select value={inquiryType} onValueChange={setInquiryType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="viewing">Schedule Viewing</SelectItem>
                  <SelectItem value="pricing">Pricing & Fees</SelectItem>
                  <SelectItem value="availability">Availability</SelectItem>
                  <SelectItem value="general">General Inquiry</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Your Name</Label>
                <Input
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Full name"
                />
              </div>
              <div className="space-y-2">
                <Label>Your Phone</Label>
                <Input
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="+254 7XX XXX XXX"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Your Email</Label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>

            <div className="space-y-2">
              <Label>Preferred Contact Method</Label>
              <Select value={preferredContact} onValueChange={setPreferredContact}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="phone">Phone Call</SelectItem>
                  <SelectItem value="whatsapp">WhatsApp</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Message</Label>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your message here..."
                rows={4}
                defaultValue={getPrefilledMessage()}
              />
            </div>

            <Button onClick={handleSendInquiry} className="w-full">
              Send Inquiry
            </Button>
          </div>
        )}

        {/* Quick Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <h5 className="font-medium text-blue-900 mb-1">💡 Contact Tips</h5>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Best calling hours: 9AM - 6PM</li>
            <li>• WhatsApp often gets faster responses</li>
            <li>• Mention you found this on BomaHub</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
