import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  PropertyWithUnits,
  PropertyAnalytics,
  InquiryWithDetails,
  BulkUnitOperation,
  PropertyUnitWithImages
} from '@/types/multiUnitProperty';
import {
  Building,
  Users,
  DollarSign,
  TrendingUp,
  CheckCircle,
  XCircle,
  Clock,
  MapPin,
  Bed,
  Bath,
  Square,
  Edit,
  Trash2,
  Eye,
  MessageSquare,
  Calendar,
  Filter,
  Download,
  MoreVertical
} from 'lucide-react';

interface LandlordDashboardProps {
  properties: PropertyWithUnits[];
  analytics: PropertyAnalytics;
  inquiries: InquiryWithDetails[];
  onPropertyEdit: (propertyId: string) => void;
  onPropertyDelete: (propertyId: string) => void;
  onUnitEdit: (propertyId: string, unitId: string) => void;
  onBulkOperation: (operation: BulkUnitOperation) => void;
  onInquiryRespond: (inquiryId: string) => void;
}

const LandlordDashboard: React.FC<LandlordDashboardProps> = ({
  properties,
  analytics,
  inquiries,
  onPropertyEdit,
  onPropertyDelete,
  onUnitEdit,
  onBulkOperation,
  onInquiryRespond
}) => {
  const [selectedProperty, setSelectedProperty] = useState<string>('all');
  const [selectedUnits, setSelectedUnits] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [filterStatus, setFilterStatus] = useState<'all' | 'available' | 'occupied'>('all');
  const [sortBy, setSortBy] = useState<'rent' | 'room_type' | 'updated'>('rent');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const handleUnitSelect = (unitId: string) => {
    const newSelected = new Set(selectedUnits);
    if (newSelected.has(unitId)) {
      newSelected.delete(unitId);
    } else {
      newSelected.add(unitId);
    }
    setSelectedUnits(newSelected);
  };

  const handleBulkAction = (operation: 'mark_available' | 'mark_unavailable' | 'update_rent') => {
    if (selectedUnits.size === 0) return;

    const bulkOperation: BulkUnitOperation = {
      unit_ids: Array.from(selectedUnits),
      operation,
      new_values: operation === 'mark_available' ? { is_available: true } :
                   operation === 'mark_unavailable' ? { is_available: false } : undefined
    };

    onBulkOperation(bulkOperation);
    setSelectedUnits(new Set());
  };

  const getFilteredUnits = () => {
    let allUnits: PropertyUnitWithImages[] = [];
    
    properties.forEach(property => {
      if (property.property_units) {
        const unitsWithProperty = property.property_units.map(unit => ({
          ...unit,
          property_title: property.title,
          property_location: property.location
        }));
        allUnits = [...allUnits, ...unitsWithProperty];
      } else {
        // Handle single unit properties
        allUnits.push({
          id: property.id,
          property_id: property.id,
          unit_name: property.title,
          room_type: property.room_type || 'apartment',
          bedrooms: property.bedrooms,
          bathrooms: property.bathrooms,
          area: property.area,
          rent: property.rent,
          is_available: property.available,
          unit_amenities: property.amenities || [],
          unit_description: property.description,
          created_at: property.created_at,
          updated_at: property.updated_at,
          unit_images: property.property_images || [],
          property_title: property.title,
          property_location: property.location
        } as PropertyUnitWithImages & { property_title: string; property_location: string });
      }
    });

    // Apply filters
    if (selectedProperty !== 'all') {
      allUnits = allUnits.filter(unit => unit.property_id === selectedProperty);
    }

    if (filterStatus !== 'all') {
      allUnits = allUnits.filter(unit => 
        filterStatus === 'available' ? unit.is_available : !unit.is_available
      );
    }

    // Apply sorting
    allUnits.sort((a, b) => {
      switch (sortBy) {
        case 'rent': return b.rent - a.rent;
        case 'room_type': return a.room_type.localeCompare(b.room_type);
        case 'updated': return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
        default: return 0;
      }
    });

    return allUnits;
  };

  const filteredUnits = getFilteredUnits();
  const recentInquiries = inquiries.slice(0, 10);

  return (
    <div className="space-y-6">
      {/* Analytics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Properties</p>
                <p className="text-2xl font-bold">{properties.length}</p>
              </div>
              <Building className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Units</p>
                <p className="text-2xl font-bold">{analytics.total_units}</p>
                <p className="text-xs text-green-600">
                  {analytics.available_units} available
                </p>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Monthly Income</p>
                <p className="text-2xl font-bold">{formatCurrency(analytics.total_monthly_income)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Occupancy Rate</p>
                <p className="text-2xl font-bold">{formatPercentage(analytics.occupancy_rate)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="units" className="space-y-4">
        <TabsList>
          <TabsTrigger value="units">Unit Management</TabsTrigger>
          <TabsTrigger value="inquiries">Inquiries ({analytics.recent_inquiries})</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Unit Management Tab */}
        <TabsContent value="units" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <CardTitle>Unit Management</CardTitle>
                  <CardDescription>
                    Manage your rental units across all properties
                  </CardDescription>
                </div>
                
                {/* Bulk Actions */}
                {selectedUnits.size > 0 && (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBulkAction('mark_available')}
                    >
                      Mark Available ({selectedUnits.size})
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBulkAction('mark_unavailable')}
                    >
                      Mark Unavailable ({selectedUnits.size})
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>

            <CardContent>
              {/* Filters */}
              <div className="flex flex-wrap gap-4 mb-6">
                <div className="space-y-2">
                  <Label className="text-sm">Property</Label>
                  <Select value={selectedProperty} onValueChange={setSelectedProperty}>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Properties</SelectItem>
                      {properties.map(property => (
                        <SelectItem key={property.id} value={property.id}>
                          {property.building_name || property.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">Status</Label>
                  <Select value={filterStatus} onValueChange={setFilterStatus as any}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Units</SelectItem>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="occupied">Occupied</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">Sort By</Label>
                  <Select value={sortBy} onValueChange={setSortBy as any}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rent">Rent</SelectItem>
                      <SelectItem value="room_type">Room Type</SelectItem>
                      <SelectItem value="updated">Last Updated</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Units Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredUnits.map((unit) => (
                  <Card key={unit.id} className="relative">
                    {/* Selection Checkbox */}
                    <div className="absolute top-3 left-3 z-10">
                      <input
                        type="checkbox"
                        checked={selectedUnits.has(unit.id)}
                        onChange={() => handleUnitSelect(unit.id)}
                        className="rounded border-gray-300"
                      />
                    </div>

                    {/* Unit Image */}
                    <div className="relative">
                      <img
                        src={unit.unit_images?.[0]?.image_url || "/placeholder.svg"}
                        alt={unit.unit_name || unit.room_type}
                        className="w-full h-32 object-cover rounded-t-lg"
                      />
                      
                      {/* Status Badge */}
                      <div className="absolute top-3 right-3">
                        {unit.is_available ? (
                          <Badge className="bg-green-500 text-white">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Available
                          </Badge>
                        ) : (
                          <Badge className="bg-red-500 text-white">
                            <XCircle className="h-3 w-3 mr-1" />
                            Occupied
                          </Badge>
                        )}
                      </div>
                    </div>

                    <CardContent className="p-4">
                      {/* Unit Info */}
                      <div className="mb-3">
                        <h4 className="font-semibold">
                          {unit.unit_name || `${unit.room_type.replace('_', ' ')} Unit`}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {(unit as any).property_title}
                        </p>
                        <p className="text-sm text-muted-foreground flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {(unit as any).property_location}
                        </p>
                      </div>

                      {/* Unit Details */}
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center space-x-3 text-sm text-muted-foreground">
                          <div className="flex items-center">
                            <Bed className="h-3 w-3 mr-1" />
                            {unit.bedrooms}
                          </div>
                          <div className="flex items-center">
                            <Bath className="h-3 w-3 mr-1" />
                            {unit.bathrooms}
                          </div>
                          {unit.area && (
                            <div className="flex items-center">
                              <Square className="h-3 w-3 mr-1" />
                              {unit.area}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Rent */}
                      <div className="mb-3">
                        <div className="text-lg font-bold text-primary">
                          {formatCurrency(unit.rent)}
                        </div>
                        <div className="text-xs text-muted-foreground">per month</div>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onUnitEdit(unit.property_id, unit.id)}
                          className="flex-1"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {/* View unit details */}}
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredUnits.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No units match your current filters.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Inquiries Tab */}
        <TabsContent value="inquiries" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Inquiries</CardTitle>
              <CardDescription>
                Manage inquiries from potential tenants
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentInquiries.map((inquiry) => (
                  <div key={inquiry.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-semibold">
                          {inquiry.unit.unit_name || inquiry.unit.room_type}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {inquiry.property.title}
                        </p>
                        <p className="text-sm text-muted-foreground flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {new Date(inquiry.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge 
                        variant={inquiry.status === 'pending' ? 'secondary' : 
                                inquiry.status === 'confirmed' ? 'default' : 'destructive'}
                      >
                        {inquiry.status}
                      </Badge>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm">
                        <span className="font-medium">Inquiry Type:</span> {inquiry.inquiry_type}
                      </p>
                      {inquiry.contact_phone && (
                        <p className="text-sm">
                          <span className="font-medium">Phone:</span> {inquiry.contact_phone}
                        </p>
                      )}
                      {inquiry.preferred_date && (
                        <p className="text-sm">
                          <span className="font-medium">Preferred Date:</span> {inquiry.preferred_date}
                        </p>
                      )}
                    </div>

                    {inquiry.message && (
                      <div className="mb-3">
                        <p className="text-sm">
                          <span className="font-medium">Message:</span>
                        </p>
                        <p className="text-sm text-muted-foreground bg-muted p-2 rounded mt-1">
                          {inquiry.message}
                        </p>
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onInquiryRespond(inquiry.id)}
                      >
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Respond
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {/* Call contact */}}
                      >
                        Call
                      </Button>
                    </div>
                  </div>
                ))}

                {recentInquiries.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No recent inquiries.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Unit Type Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Unit Type Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.unit_type_breakdown.map((unitType) => (
                    <div key={unitType.room_type} className="flex justify-between items-center">
                      <div>
                        <p className="font-medium capitalize">
                          {unitType.room_type.replace('_', ' ')}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {unitType.occupied}/{unitType.total} occupied
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">
                          {formatCurrency(unitType.average_rent)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {formatPercentage(unitType.occupied / unitType.total)} occupied
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Pending Applications:</span>
                    <span className="font-semibold">{analytics.pending_applications}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recent Inquiries:</span>
                    <span className="font-semibold">{analytics.recent_inquiries}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Maintenance Units:</span>
                    <span className="font-semibold">{analytics.maintenance_units}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LandlordDashboard;
