import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { MapPin, Navigation, Target, Edit } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface LocationInputProps {
  value: string;
  onChange: (value: string, coordinates?: { lat: number; lng: number }) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  className?: string;
  coordinates?: { lat: number; lng: number };
}

// Common locations in Kenya with approximate coordinates
const POPULAR_LOCATIONS = {
  'Nairobi': {
    'Kilimani': { lat: -1.2921, lng: 36.8219 },
    'Westlands': { lat: -1.2672, lng: 36.8103 },
    '<PERSON>': { lat: -1.3197, lng: 36.6859 },
    'Lavington': { lat: -1.2833, lng: 36.7833 },
    'Ki<PERSON>leshwa': { lat: -1.2858, lng: 36.7833 },
    'Parklands': { lat: -1.2500, lng: 36.8500 },
    'Eastleigh': { lat: -1.2667, lng: 36.8667 },
    'South B': { lat: -1.3167, lng: 36.8333 },
    'South C': { lat: -1.3000, lng: 36.8167 },
    'Embakasi': { lat: -1.3167, lng: 36.9000 },
    'Kasarani': { lat: -1.2167, lng: 36.9000 },
    'Roysambu': { lat: -1.2167, lng: 36.8833 },
    'Kahawa': { lat: -1.1833, lng: 36.9167 },
    'Thika Road': { lat: -1.2333, lng: 36.9000 },
    'Ngong Road': { lat: -1.3000, lng: 36.7833 },
    'Mombasa Road': { lat: -1.3333, lng: 36.8667 },
    'Lang\'ata': { lat: -1.3667, lng: 36.7333 }
  },
  'Mombasa': {
    'Nyali': { lat: -4.0333, lng: 39.7167 },
    'Bamburi': { lat: -4.0167, lng: 39.7333 },
    'Shanzu': { lat: -4.0000, lng: 39.7333 },
    'Tudor': { lat: -4.0667, lng: 39.6833 },
    'Kizingo': { lat: -4.0667, lng: 39.6667 },
    'Old Town': { lat: -4.0667, lng: 39.6833 }
  },
  'Kisumu': {
    'Milimani': { lat: -0.0917, lng: 34.7681 },
    'Kondele': { lat: -0.1000, lng: 34.7500 },
    'Mamboleo': { lat: -0.0833, lng: 34.7833 }
  },
  'Nakuru': {
    'Milimani': { lat: -0.3031, lng: 36.0800 },
    'Section 58': { lat: -0.2833, lng: 36.0667 },
    'Lanet': { lat: -0.2500, lng: 36.1000 }
  }
};

const LocationInput: React.FC<LocationInputProps> = ({
  value,
  onChange,
  placeholder = "e.g., Kilimani, Nairobi",
  label,
  error,
  className = "",
  coordinates
}) => {
  const [suggestions, setSuggestions] = useState<Array<{ location: string; coordinates?: { lat: number; lng: number } }>>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showCoordinateInput, setShowCoordinateInput] = useState(false);
  const [manualLat, setManualLat] = useState(coordinates?.lat?.toString() || '');
  const [manualLng, setManualLng] = useState(coordinates?.lng?.toString() || '');

  useEffect(() => {
    if (value.length > 2) {
      const allSuggestions: Array<{ location: string; coordinates?: { lat: number; lng: number } }> = [];
      
      Object.entries(POPULAR_LOCATIONS).forEach(([county, locations]) => {
        Object.entries(locations).forEach(([area, coords]) => {
          const fullLocation = `${area}, ${county}`;
          if (fullLocation.toLowerCase().includes(value.toLowerCase())) {
            allSuggestions.push({
              location: fullLocation,
              coordinates: coords
            });
          }
        });
      });
      
      setSuggestions(allSuggestions.slice(0, 5));
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [value]);

  const handleSuggestionClick = (suggestion: { location: string; coordinates?: { lat: number; lng: number } }) => {
    onChange(suggestion.location, suggestion.coordinates);
    setShowSuggestions(false);
  };

  const handleUseCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser. Please enter the location manually or use the coordinate input.');
      return;
    }

    // Check if the page is served over HTTPS or localhost
    const isSecureContext = window.location.protocol === 'https:' || 
                           window.location.hostname === 'localhost' || 
                           window.location.hostname === '127.0.0.1';

    if (!isSecureContext) {
      alert('Location access requires a secure connection (HTTPS). Please enter the location manually or use the coordinate input.');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        
        // Use reverse geocoding to get a readable address
        fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&accept-language=en`)
          .then(response => {
            if (!response.ok) {
              throw new Error('Geocoding service unavailable');
            }
            return response.json();
          })
          .then(data => {
            // Try to extract a meaningful address
            let address = '';
            if (data.address) {
              const parts = [];
              if (data.address.suburb || data.address.neighbourhood) {
                parts.push(data.address.suburb || data.address.neighbourhood);
              }
              if (data.address.city || data.address.town || data.address.county) {
                parts.push(data.address.city || data.address.town || data.address.county);
              }
              address = parts.length > 0 ? parts.join(', ') : data.display_name;
            } else {
              address = `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
            }
            
            onChange(address, { lat: latitude, lng: longitude });
          })
          .catch((error) => {
            console.warn('Reverse geocoding failed:', error);
            // Fallback to coordinates only
            onChange(`${latitude.toFixed(4)}, ${longitude.toFixed(4)}`, { lat: latitude, lng: longitude });
          });
      },
      (error) => {
        console.error('Geolocation error:', error);
        
        let errorMessage = 'Unable to get your current location. ';
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage += 'Location access was denied. Please allow location access in your browser settings or enter the location manually.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage += 'Location information is unavailable. Please check your GPS/network connection or enter the location manually.';
            break;
          case error.TIMEOUT:
            errorMessage += 'Location request timed out. Please try again or enter the location manually.';
            break;
          default:
            errorMessage += 'Please enter the location manually or use the coordinate input.';
            break;
        }
        
        alert(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  const handleManualCoordinates = () => {
    const lat = parseFloat(manualLat);
    const lng = parseFloat(manualLng);
    
    if (isNaN(lat) || isNaN(lng)) {
      alert('Please enter valid latitude and longitude values. Example: Latitude: -1.2921, Longitude: 36.8219');
      return;
    }
    
    if (lat < -90 || lat > 90) {
      alert('Latitude must be between -90 and 90 degrees. Example for Nairobi: -1.2921');
      return;
    }
    
    if (lng < -180 || lng > 180) {
      alert('Longitude must be between -180 and 180 degrees. Example for Nairobi: 36.8219');
      return;
    }
    
    // For Kenya, provide more specific validation
    if (lat > 5 || lat < -5 || lng < 33 || lng > 42) {
      const confirmOutsideKenya = confirm(
        'These coordinates appear to be outside Kenya. Are you sure you want to continue?\n\n' +
        'For reference, Kenya coordinates are approximately:\n' +
        'Latitude: 5°N to 5°S (-5 to 5)\n' +
        'Longitude: 33°E to 42°E (33 to 42)'
      );
      if (!confirmOutsideKenya) {
        return;
      }
    }
    
    // Use reverse geocoding to get a readable address
    fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=en`)
      .then(response => {
        if (!response.ok) {
          throw new Error('Geocoding service unavailable');
        }
        return response.json();
      })
      .then(data => {
        // Try to extract a meaningful address
        let address = '';
        if (data.address) {
          const parts = [];
          if (data.address.suburb || data.address.neighbourhood) {
            parts.push(data.address.suburb || data.address.neighbourhood);
          }
          if (data.address.city || data.address.town || data.address.county) {
            parts.push(data.address.city || data.address.town || data.address.county);
          }
          address = parts.length > 0 ? parts.join(', ') : data.display_name;
        } else {
          address = `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
        }
        
        onChange(address, { lat, lng });
        setShowCoordinateInput(false);
        setManualLat('');
        setManualLng('');
      })
      .catch((error) => {
        console.warn('Reverse geocoding failed:', error);
        // Fallback to coordinates only
        onChange(`${lat.toFixed(4)}, ${lng.toFixed(4)}`, { lat, lng });
        setShowCoordinateInput(false);
        setManualLat('');
        setManualLng('');
      });
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {label && <Label>{label}</Label>}
      <div className="relative">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Input
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder={placeholder}
              className={error ? 'border-red-500' : ''}
              onFocus={() => value.length > 2 && setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            />
            
            {showSuggestions && suggestions.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    type="button"
                    className="w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center gap-2"
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    <MapPin className="h-4 w-4 text-gray-400" />
                    {suggestion.location}
                  </button>
                ))}
              </div>
            )}
          </div>
          
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={handleUseCurrentLocation}
            title="Use current location"
          >
            <Navigation className="h-4 w-4" />
          </Button>
          
          <Dialog open={showCoordinateInput} onOpenChange={setShowCoordinateInput}>
            <DialogTrigger asChild>
              <Button
                type="button"
                variant="outline"
                size="icon"
                title="Enter exact coordinates"
              >
                <Target className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Enter Exact Coordinates</DialogTitle>
                <DialogDescription>
                  Enter the precise latitude and longitude for the property location.
                  You can get these from Google Maps by right-clicking on the location.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="bg-blue-50 p-3 rounded-lg text-sm">
                  <h4 className="font-medium text-blue-900 mb-2">Example Coordinates for Kenya:</h4>
                  <div className="text-blue-800 space-y-1">
                    <div>• Nairobi CBD: -1.2921, 36.8219</div>
                    <div>• Mombasa: -4.0435, 39.6682</div>
                    <div>• Kisumu: -0.0917, 34.7681</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="latitude">Latitude</Label>
                  <Input
                    id="latitude"
                    value={manualLat}
                    onChange={(e) => setManualLat(e.target.value)}
                    placeholder="e.g., -1.2921"
                    type="number"
                    step="any"
                  />
                  <p className="text-xs text-muted-foreground">Range: -90 to 90 (Negative for South)</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="longitude">Longitude</Label>
                  <Input
                    id="longitude"
                    value={manualLng}
                    onChange={(e) => setManualLng(e.target.value)}
                    placeholder="e.g., 36.8219"
                    type="number"
                    step="any"
                  />
                  <p className="text-xs text-muted-foreground">Range: -180 to 180 (Positive for East)</p>
                </div>
                <div className="flex gap-2 justify-end">
                  <Button variant="outline" onClick={() => setShowCoordinateInput(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleManualCoordinates}>
                    Set Coordinates
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      {error && <p className="text-sm text-red-500">{error}</p>}
      
      {coordinates && (
        <div className="text-xs text-green-600 bg-green-50 p-2 rounded flex items-center gap-1">
          <MapPin className="h-3 w-3" />
          Coordinates: {coordinates.lat.toFixed(4)}, {coordinates.lng.toFixed(4)}
        </div>
      )}
      
      <p className="text-xs text-muted-foreground">
        Start typing for suggestions, use <Navigation className="h-3 w-3 inline" /> for current location (requires HTTPS), or <Target className="h-3 w-3 inline" /> for exact coordinates
      </p>
    </div>
  );
};

export default LocationInput;
