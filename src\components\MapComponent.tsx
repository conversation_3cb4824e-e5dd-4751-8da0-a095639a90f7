import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import { MapPin, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import L from 'leaflet';

// Fix for default markers in React Leaflet
import 'leaflet/dist/leaflet.css';

// Custom marker icon
const customIcon = new L.Icon({
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

interface MapComponentProps {
  latitude?: number;
  longitude?: number;
  location: string;
  title: string;
  className?: string;
}

const MapComponent: React.FC<MapComponentProps> = ({
  latitude,
  longitude,
  location,
  title,
  className = "h-64 w-full rounded-lg"
}) => {
  const openInGoogleMaps = () => {
    if (latitude && longitude) {
      window.open(`https://www.google.com/maps?q=${latitude},${longitude}`, '_blank');
    } else {
      window.open(`https://www.google.com/maps/search/${encodeURIComponent(location)}`, '_blank');
    }
  };

  const openInAppleMaps = () => {
    if (latitude && longitude) {
      window.open(`https://maps.apple.com/?q=${latitude},${longitude}`, '_blank');
    } else {
      window.open(`https://maps.apple.com/?q=${encodeURIComponent(location)}`, '_blank');
    }
  };

  // If coordinates are available, show embedded interactive map
  if (latitude && longitude) {
    return (
      <div className={className}>
        <div className="relative h-full w-full rounded-lg overflow-hidden">
          <MapContainer
            center={[latitude, longitude]}
            zoom={16}
            style={{ height: '100%', width: '100%' }}
            zoomControl={true}
            scrollWheelZoom={false}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <Marker position={[latitude, longitude]} icon={customIcon}>
              <Popup>
                <div className="text-center p-2">
                  <strong className="text-sm font-semibold">{title}</strong>
                  <br />
                  <span className="text-xs text-gray-600">{location}</span>
                  <br />
                  <div className="mt-2 flex gap-1">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={openInGoogleMaps}
                      className="text-xs h-6"
                    >
                      Google Maps
                    </Button>
                  </div>
                </div>
              </Popup>
            </Marker>
          </MapContainer>
          
          {/* Overlay buttons */}
          <div className="absolute top-2 right-2 flex flex-col gap-1">
            <Button
              size="sm"
              variant="secondary"
              onClick={openInGoogleMaps}
              className="bg-white/90 hover:bg-white text-xs h-7"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Directions
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // If no coordinates, show placeholder with links to search on maps
  return (
    <div className={`bg-muted rounded-lg flex flex-col items-center justify-center p-6 ${className}`}>
      <MapPin className="h-12 w-12 mb-4 text-muted-foreground" />
      <h3 className="text-lg font-medium text-foreground mb-2">Exact Location</h3>
      <p className="text-muted-foreground text-center mb-4">{location}</p>
      
      <div className="text-xs text-muted-foreground mb-4 text-center">
        Precise coordinates not available.<br />
        <strong>Ask the landlord for exact location details.</strong>
      </div>
      
      <div className="flex gap-2 flex-wrap justify-center">
        <Button
          size="sm"
          variant="outline"
          onClick={openInGoogleMaps}
        >
          <ExternalLink className="h-3 w-3 mr-1" />
          Search on Maps
        </Button>
      </div>
    </div>
  );
};

export default MapComponent;
