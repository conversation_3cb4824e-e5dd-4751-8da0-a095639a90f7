import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MultiUnitPropertyFormData, 
  UnitFormData, 
  ENHANCED_ROOM_TYPES, 
  PROPERTY_TYPES,
  BUILDING_AMENITIES,
  UNIT_AMENITIES
} from '@/types/multiUnitProperty';
import { K<PERSON>YAN_COUNTIES } from '@/types/property';
import { useAuth } from '@/hooks/useAuth';
import { 
  Plus, 
  Minus, 
  Upload, 
  X, 
  Building, 
  Home,
  Copy,
  Trash2
} from 'lucide-react';
import LocationInput from '@/components/LocationInput';
import Navigation from '@/components/Navigation';

const MultiUnitPropertyForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [activeTab, setActiveTab] = useState('basic');
  const [formData, setFormData] = useState<MultiUnitPropertyFormData>({
    title: '',
    building_name: '',
    description: '',
    location: '',
    county: '',
    property_type: 'single_unit',
    amenities: [],
    featured: false,
    latitude: undefined,
    longitude: undefined,
    formatted_address: undefined,
    neighborhood: undefined,
    city: undefined,
    units: []
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [images, setImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  // Initialize with one unit for single unit properties
  React.useEffect(() => {
    if (formData.property_type === 'single_unit' && formData.units.length === 0) {
      setFormData(prev => ({
        ...prev,
        units: [{
          room_type: '',
          bedrooms: 1,
          bathrooms: 1,
          area: 0,
          rent: 0,
          deposit: 0,
          is_available: true,
          unit_amenities: [],
          unit_description: '',
          floor_number: 1,
          unit_number: '',
          images: []
        }]
      }));
    }
  }, [formData.property_type, formData.units.length]);

  const handleInputChange = (field: keyof MultiUnitPropertyFormData, value: string | boolean | string[] | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePropertyTypeChange = (type: 'single_unit' | 'multi_unit') => {
    setFormData(prev => ({
      ...prev,
      property_type: type,
      units: type === 'single_unit' ? prev.units.slice(0, 1) : prev.units
    }));
  };

  const addUnit = () => {
    const newUnit: UnitFormData = {
      room_type: '',
      bedrooms: 1,
      bathrooms: 1,
      area: 0,
      rent: 0,
      deposit: 0,
      is_available: true,
      unit_amenities: [],
      unit_description: '',
      floor_number: 1,
      unit_number: '',
      images: []
    };
    
    setFormData(prev => ({
      ...prev,
      units: [...prev.units, newUnit]
    }));
  };

  const removeUnit = (index: number) => {
    if (formData.units.length > 1) {
      setFormData(prev => ({
        ...prev,
        units: prev.units.filter((_, i) => i !== index)
      }));
    }
  };

  const updateUnit = (index: number, field: keyof UnitFormData, value: string | number | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      units: prev.units.map((unit, i) => 
        i === index ? { ...unit, [field]: value } : unit
      )
    }));
  };

  const duplicateUnit = (index: number) => {
    const unitToDuplicate = { ...formData.units[index] };
    unitToDuplicate.unit_number = '';
    unitToDuplicate.images = [];
    
    setFormData(prev => ({
      ...prev,
      units: [...prev.units, unitToDuplicate]
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length + images.length > 10) {
      alert('Maximum 10 images allowed');
      return;
    }

    setImages(prev => [...prev, ...files]);
    
    // Create previews
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const handleBuildingAmenityChange = (amenity: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      amenities: checked 
        ? [...prev.amenities, amenity]
        : prev.amenities.filter(a => a !== amenity)
    }));
  };

  const handleUnitAmenityChange = (unitIndex: number, amenity: string, checked: boolean) => {
    updateUnit(unitIndex, 'unit_amenities', 
      checked 
        ? [...formData.units[unitIndex].unit_amenities, amenity]
        : formData.units[unitIndex].unit_amenities.filter(a => a !== amenity)
    );
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Property title is required';
    }
    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }
    if (!formData.county) {
      newErrors.county = 'County is required';
    }

    // Validate units
    formData.units.forEach((unit, index) => {
      if (!unit.room_type) {
        newErrors[`unit_${index}_room_type`] = 'Room type is required';
      }
      if (unit.rent <= 0) {
        newErrors[`unit_${index}_rent`] = 'Rent must be greater than 0';
      }
      if (unit.bedrooms < 1) {
        newErrors[`unit_${index}_bedrooms`] = 'Bedrooms must be at least 1';
      }
      if (unit.bathrooms < 1) {
        newErrors[`unit_${index}_bathrooms`] = 'Bathrooms must be at least 1';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      // TODO: Implement API call to create multi-unit property
      console.log('Creating multi-unit property:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      navigate('/my-properties');
    } catch (error) {
      console.error('Error creating property:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderUnitForm = (unit: UnitFormData, index: number) => (
    <Card key={index} className="relative">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">
            Unit {index + 1}
            {unit.unit_number && ` - ${unit.unit_number}`}
          </CardTitle>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => duplicateUnit(index)}
              className="text-xs"
            >
              <Copy className="h-3 w-3 mr-1" />
              Duplicate
            </Button>
            {formData.units.length > 1 && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removeUnit(index)}
                className="text-xs text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Remove
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Unit Basic Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Unit Name/Number</Label>
            <Input
              value={unit.unit_number || ''}
              onChange={(e) => updateUnit(index, 'unit_number', e.target.value)}
              placeholder="e.g., A1, B2, Studio 1"
            />
          </div>
          
          <div className="space-y-2">
            <Label>Room Type *</Label>
            <Select
              value={unit.room_type}
              onValueChange={(value) => updateUnit(index, 'room_type', value)}
            >
              <SelectTrigger className={errors[`unit_${index}_room_type`] ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select Room Type" />
              </SelectTrigger>
              <SelectContent>
                {ENHANCED_ROOM_TYPES.map(roomType => (
                  <SelectItem key={roomType.value} value={roomType.value}>
                    <div>
                      <div className="font-medium">{roomType.label}</div>
                      <div className="text-xs text-muted-foreground">{roomType.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors[`unit_${index}_room_type`] && (
              <p className="text-sm text-red-500">{errors[`unit_${index}_room_type`]}</p>
            )}
          </div>
        </div>

        {/* Unit Details */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label>Bedrooms *</Label>
            <Input
              type="number"
              min="1"
              value={unit.bedrooms}
              onChange={(e) => updateUnit(index, 'bedrooms', parseInt(e.target.value) || 1)}
              className={errors[`unit_${index}_bedrooms`] ? 'border-red-500' : ''}
            />
            {errors[`unit_${index}_bedrooms`] && (
              <p className="text-sm text-red-500">{errors[`unit_${index}_bedrooms`]}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label>Bathrooms *</Label>
            <Input
              type="number"
              min="1"
              value={unit.bathrooms}
              onChange={(e) => updateUnit(index, 'bathrooms', parseInt(e.target.value) || 1)}
              className={errors[`unit_${index}_bathrooms`] ? 'border-red-500' : ''}
            />
            {errors[`unit_${index}_bathrooms`] && (
              <p className="text-sm text-red-500">{errors[`unit_${index}_bathrooms`]}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label>Area (sq ft)</Label>
            <Input
              type="number"
              value={unit.area || ''}
              onChange={(e) => updateUnit(index, 'area', parseFloat(e.target.value) || 0)}
              placeholder="1200"
            />
          </div>
          
          <div className="space-y-2">
            <Label>Floor</Label>
            <Input
              type="number"
              min="1"
              value={unit.floor_number || ''}
              onChange={(e) => updateUnit(index, 'floor_number', parseInt(e.target.value) || 1)}
              placeholder="1"
            />
          </div>
        </div>

        {/* Rent and Deposit */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Monthly Rent (KES) *</Label>
            <Input
              type="number"
              value={unit.rent}
              onChange={(e) => updateUnit(index, 'rent', parseFloat(e.target.value) || 0)}
              placeholder="50000"
              className={errors[`unit_${index}_rent`] ? 'border-red-500' : ''}
            />
            {errors[`unit_${index}_rent`] && (
              <p className="text-sm text-red-500">{errors[`unit_${index}_rent`]}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label>Security Deposit (KES)</Label>
            <Input
              type="number"
              value={unit.deposit || ''}
              onChange={(e) => updateUnit(index, 'deposit', parseFloat(e.target.value) || 0)}
              placeholder="50000"
            />
          </div>
        </div>

        {/* Unit Description */}
        <div className="space-y-2">
          <Label>Unit Description</Label>
          <Textarea
            value={unit.unit_description || ''}
            onChange={(e) => updateUnit(index, 'unit_description', e.target.value)}
            placeholder="Describe this specific unit..."
            rows={3}
          />
        </div>

        {/* Unit Amenities */}
        <div className="space-y-2">
          <Label>Unit-Specific Amenities</Label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {UNIT_AMENITIES.map(amenity => (
              <div key={amenity} className="flex items-center space-x-2">
                <Checkbox
                  id={`unit_${index}_${amenity}`}
                  checked={unit.unit_amenities.includes(amenity)}
                  onCheckedChange={(checked) => 
                    handleUnitAmenityChange(index, amenity, checked as boolean)
                  }
                />
                <Label htmlFor={`unit_${index}_${amenity}`} className="text-sm">
                  {amenity}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Availability */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id={`unit_${index}_available`}
            checked={unit.is_available}
            onCheckedChange={(checked) => updateUnit(index, 'is_available', checked)}
          />
          <Label htmlFor={`unit_${index}_available`}>
            Unit is currently available for rent
          </Label>
        </div>
      </CardContent>
    </Card>
  );

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-6xl mx-auto">
          <CardHeader>
            <CardTitle className="text-2xl">List Your Property for Rent</CardTitle>
            <CardDescription>
              Create a comprehensive listing for your rental property. Choose between single unit or multi-unit property.
            </CardDescription>
          </CardHeader>
        
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">Basic Information</TabsTrigger>
                  <TabsTrigger value="units">Units & Details</TabsTrigger>
                  <TabsTrigger value="amenities">Amenities & Features</TabsTrigger>
                  <TabsTrigger value="images">Images</TabsTrigger>
                </TabsList>

                {/* Basic Information Tab */}
                <TabsContent value="basic" className="space-y-6">
                  {/* Property Type Selection */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Property Type</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {PROPERTY_TYPES.map(type => (
                          <div
                            key={type.value}
                            className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                              formData.property_type === type.value
                                ? 'border-primary bg-primary/5'
                                : 'border-border hover:border-primary/50'
                            }`}
                            onClick={() => handlePropertyTypeChange(type.value)}
                          >
                            <div className="flex items-center space-x-3">
                              {type.value === 'single_unit' ? (
                                <Home className="h-8 w-8 text-primary" />
                              ) : (
                                <Building className="h-8 w-8 text-primary" />
                              )}
                              <div>
                                <h4 className="font-semibold">{type.label}</h4>
                                <p className="text-sm text-muted-foreground">{type.description}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Basic Property Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Property Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Property Title *</Label>
                          <Input
                            value={formData.title}
                            onChange={(e) => handleInputChange('title', e.target.value)}
                            placeholder="e.g., Modern Apartment Complex in Kilimani"
                            className={errors.title ? 'border-red-500' : ''}
                          />
                          {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
                        </div>

                        {formData.property_type === 'multi_unit' && (
                          <div className="space-y-2">
                            <Label>Building Name</Label>
                            <Input
                              value={formData.building_name || ''}
                              onChange={(e) => handleInputChange('building_name', e.target.value)}
                              placeholder="e.g., Sunrise Apartments"
                            />
                          </div>
                        )}
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <LocationInput
                            value={formData.location}
                            onChange={(value, coordinates) => {
                              handleInputChange('location', value);
                              if (coordinates) {
                                setFormData(prev => ({
                                  ...prev,
                                  latitude: coordinates.lat,
                                  longitude: coordinates.lng
                                }));
                              }
                            }}
                            label="Location *"
                            placeholder="e.g., Kilimani, Nairobi"
                            error={errors.location}
                            coordinates={formData.latitude && formData.longitude ? {
                              lat: formData.latitude,
                              lng: formData.longitude
                            } : undefined}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>County *</Label>
                          <Select
                            value={formData.county}
                            onValueChange={(value) => handleInputChange('county', value)}
                          >
                            <SelectTrigger className={errors.county ? 'border-red-500' : ''}>
                              <SelectValue placeholder="Select County" />
                            </SelectTrigger>
                            <SelectContent>
                              {KENYAN_COUNTIES.map(county => (
                                <SelectItem key={county} value={county}>
                                  {county}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.county && <p className="text-sm text-red-500">{errors.county}</p>}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Property Description</Label>
                        <Textarea
                          value={formData.description || ''}
                          onChange={(e) => handleInputChange('description', e.target.value)}
                          placeholder="Describe your property..."
                          rows={4}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Units Tab */}
                <TabsContent value="units" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">
                          {formData.property_type === 'multi_unit' ? 'Property Units' : 'Unit Details'}
                        </CardTitle>
                        {formData.property_type === 'multi_unit' && (
                          <Button
                            type="button"
                            variant="outline"
                            onClick={addUnit}
                            size="sm"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Unit
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {formData.units.map((unit, index) => renderUnitForm(unit, index))}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Amenities Tab */}
                <TabsContent value="amenities" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">
                        {formData.property_type === 'multi_unit' ? 'Building Amenities' : 'Property Amenities'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {BUILDING_AMENITIES.map(amenity => (
                          <div key={amenity} className="flex items-center space-x-2">
                            <Checkbox
                              id={amenity}
                              checked={formData.amenities.includes(amenity)}
                              onCheckedChange={(checked) => 
                                handleBuildingAmenityChange(amenity, checked as boolean)
                              }
                            />
                            <Label htmlFor={amenity} className="text-sm">
                              {amenity}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="featured"
                          checked={formData.featured}
                          onCheckedChange={(checked) => handleInputChange('featured', checked)}
                        />
                        <Label htmlFor="featured">
                          Feature this property (highlighted in searches)
                        </Label>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Images Tab */}
                <TabsContent value="images" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Property Images</CardTitle>
                      <CardDescription>
                        Add photos to showcase your property and attract more tenants
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
                          <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                          <div className="space-y-4">
                            <p className="text-center text-sm text-gray-500">
                              Add photos to showcase your property
                            </p>
                            
                            {/* Upload Options */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              <Button
                                type="button"
                                variant="outline"
                                className="w-full"
                                onClick={() => document.getElementById('camera-input')?.click()}
                              >
                                <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                  <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                                  <circle cx="12" cy="13" r="4"/>
                                </svg>
                                Take Photo
                              </Button>
                              
                              <Button
                                type="button"
                                variant="outline"
                                className="w-full"
                                onClick={() => document.getElementById('gallery-input')?.click()}
                              >
                                <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                  <circle cx="9" cy="9" r="2"/>
                                  <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                                </svg>
                                Choose from Gallery
                              </Button>
                              
                              <Button
                                type="button"
                                variant="outline"
                                className="w-full"
                                onClick={() => document.getElementById('files-input')?.click()}
                              >
                                <Upload className="h-4 w-4 mr-2" />
                                Browse Files
                              </Button>
                            </div>
                            
                            {/* Hidden file inputs */}
                            <Input
                              id="camera-input"
                              type="file"
                              multiple
                              accept="image/*"
                              capture="environment"
                              onChange={handleImageUpload}
                              className="hidden"
                            />
                            <Input
                              id="gallery-input"
                              type="file"
                              multiple
                              accept="image/*"
                              onChange={handleImageUpload}
                              className="hidden"
                            />
                            <Input
                              id="files-input"
                              type="file"
                              multiple
                              accept="image/*"
                              onChange={handleImageUpload}
                              className="hidden"
                            />
                            
                            <p className="text-center text-xs text-gray-500">
                              Maximum 10 images, 5MB each • Supported formats: JPG, PNG, WebP
                            </p>
                          </div>
                        </div>

                        {imagePreviews.length > 0 && (
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {imagePreviews.map((preview, index) => (
                              <div key={index} className="relative group">
                                <img
                                  src={preview}
                                  alt={`Preview ${index + 1}`}
                                  className="w-full h-32 object-cover rounded-lg"
                                />
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="sm"
                                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                  onClick={() => removeImage(index)}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate(-1)}
                  disabled={loading}
                >
                  Cancel
                </Button>
                
                <div className="flex gap-3">
                  {activeTab !== 'basic' && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        if (activeTab === 'units') setActiveTab('basic');
                        if (activeTab === 'amenities') setActiveTab('units');
                        if (activeTab === 'images') setActiveTab('amenities');
                      }}
                    >
                      Previous
                    </Button>
                  )}
                  
                  {activeTab !== 'images' ? (
                    <Button
                      type="button"
                      onClick={() => {
                        if (activeTab === 'basic') setActiveTab('units');
                        if (activeTab === 'units') setActiveTab('amenities');
                        if (activeTab === 'amenities') setActiveTab('images');
                      }}
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      disabled={loading}
                      className="min-w-[120px]"
                    >
                      {loading ? (
                        <>
                          <Plus className="mr-2 h-4 w-4 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Plus className="mr-2 h-4 w-4" />
                          Create Property
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MultiUnitPropertyForm;
