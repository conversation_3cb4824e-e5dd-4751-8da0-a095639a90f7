import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X, MapPin, Bed, Bath, Square, Phone, Mail, Star } from 'lucide-react';
import { PropertyWithImages } from '@/types/property';

interface PropertyComparisonProps {
  properties: PropertyWithImages[];
  onRemoveProperty: (propertyId: string) => void;
  onClearAll: () => void;
}

export const PropertyComparison: React.FC<PropertyComparisonProps> = ({
  properties,
  onRemoveProperty,
  onClearAll
}) => {
  if (properties.length === 0) {
    return null;
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getComparisonData = () => {
    if (properties.length < 2) return null;

    const prices = properties.map(p => p.rent);
    const areas = properties.map(p => p.area);
    const pricePerSqft = properties.map(p => p.rent / p.area);

    return {
      cheapest: Math.min(...prices),
      mostExpensive: Math.max(...prices),
      largestArea: Math.max(...areas),
      smallestArea: Math.min(...areas),
      bestValue: Math.min(...pricePerSqft),
      worstValue: Math.max(...pricePerSqft)
    };
  };

  const comparison = getComparisonData();

  const getBestValueBadge = (property: PropertyWithImages) => {
    if (!comparison) return null;
    const pricePerSqft = property.rent / property.area;
    
    if (property.rent === comparison.cheapest) {
      return <Badge className="bg-green-100 text-green-800">Cheapest</Badge>;
    }
    if (pricePerSqft === comparison.bestValue) {
      return <Badge className="bg-blue-100 text-blue-800">Best Value</Badge>;
    }
    if (property.area === comparison.largestArea) {
      return <Badge className="bg-purple-100 text-purple-800">Largest</Badge>;
    }
    return null;
  };

  return (
    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            Compare Properties ({properties.length})
          </CardTitle>
          <Button variant="outline" size="sm" onClick={onClearAll}>
            Clear All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 min-w-full">
            {properties.map((property) => (
              <div key={property.id} className="border rounded-lg p-4 relative">
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 h-6 w-6 p-0"
                  onClick={() => onRemoveProperty(property.id)}
                >
                  <X className="h-4 w-4" />
                </Button>

                {/* Property Image */}
                <div className="aspect-video bg-muted rounded-lg mb-3 overflow-hidden">
                  {property.property_images?.[0] ? (
                    <img
                      src={property.property_images[0].image_url}
                      alt={property.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                      No Image
                    </div>
                  )}
                </div>

                {/* Badges */}
                <div className="flex flex-wrap gap-1 mb-2">
                  {property.featured && (
                    <Badge variant="secondary">
                      <Star className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                  {getBestValueBadge(property)}
                </div>

                {/* Property Info */}
                <h3 className="font-semibold text-sm mb-2 line-clamp-2">{property.title}</h3>
                
                <div className="flex items-center text-muted-foreground mb-2 text-xs">
                  <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span className="truncate">{property.location}</span>
                </div>

                {/* Key Metrics */}
                <div className="space-y-2 mb-3">
                  <div className="flex justify-between">
                    <span className="text-xs text-muted-foreground">Monthly Rent</span>
                    <span className="font-semibold text-sm">{formatPrice(property.rent)}</span>
                  </div>
                  
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center">
                      <Bed className="h-3 w-3 mr-1" />
                      <span>{property.bedrooms}</span>
                    </div>
                    <div className="flex items-center">
                      <Bath className="h-3 w-3 mr-1" />
                      <span>{property.bathrooms}</span>
                    </div>
                    <div className="flex items-center">
                      <Square className="h-3 w-3 mr-1" />
                      <span>{property.area}</span>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-xs text-muted-foreground">Price per sqft</span>
                    <span className="text-xs">KSH {Math.round(property.rent / property.area)}</span>
                  </div>
                </div>

                {/* Amenities */}
                <div className="mb-3">
                  <div className="text-xs text-muted-foreground mb-1">Amenities</div>
                  <div className="flex flex-wrap gap-1">
                    {property.amenities?.slice(0, 3).map((amenity) => (
                      <Badge key={amenity} variant="outline" className="text-xs">
                        {amenity}
                      </Badge>
                    ))}
                    {property.amenities && property.amenities.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{property.amenities.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Quick Contact */}
                <div className="flex gap-2">
                  <Button size="sm" className="flex-1 text-xs h-8">
                    <Phone className="h-3 w-3 mr-1" />
                    Call
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1 text-xs h-8">
                    <Mail className="h-3 w-3 mr-1" />
                    Email
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Comparison Summary */}
        {comparison && properties.length >= 2 && (
          <div className="mt-6 p-4 bg-muted/30 rounded-lg">
            <h4 className="font-semibold mb-3">Comparison Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground">Price Range</div>
                <div className="font-medium">
                  {formatPrice(comparison.cheapest)} - {formatPrice(comparison.mostExpensive)}
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Area Range</div>
                <div className="font-medium">
                  {comparison.smallestArea} - {comparison.largestArea} sqft
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Best Value</div>
                <div className="font-medium">
                  KSH {Math.round(comparison.bestValue)}/sqft
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Total Properties</div>
                <div className="font-medium">{properties.length} compared</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
