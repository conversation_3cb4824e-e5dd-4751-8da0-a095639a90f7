import { useState, useEffect } from "react";
import { PropertyWithImages, SearchFilters } from "@/types/property";
import PropertyCard from "./PropertyCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Search, SlidersHorizontal, Plus } from "lucide-react";

interface PropertyGridProps {
  properties: PropertyWithImages[];
  onViewDetails: (propertyId: string) => void;
  onAddToCompare?: (property: PropertyWithImages) => void;
  compareProperties?: PropertyWithImages[];
  loading?: boolean;
  filters?: SearchFilters;
}

const PropertyGrid = ({ 
  properties, 
  onViewDetails, 
  onAddToCompare,
  compareProperties = [],
  loading = false,
  filters = {}
}: PropertyGridProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("recent");
  const [filterBedrooms, setFilterBedrooms] = useState("all");
  const [filterCounty, setFilterCounty] = useState("all");

  // Update local filters when external filters change
  useEffect(() => {
    if (filters.location) setSearchTerm(filters.location);
    if (filters.county) setFilterCounty(filters.county);
    if (filters.bedrooms) setFilterBedrooms(filters.bedrooms.toString());
  }, [filters]);

  const counties = Array.from(new Set(properties.map(p => p.county)));

  const filteredProperties = properties.filter(property => {
    // Basic search and filter logic
    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBedrooms = filterBedrooms === "all" || property.bedrooms.toString() === filterBedrooms;
    const matchesCounty = filterCounty === "all" || property.county === filterCounty;
    
    // Advanced filters from SearchFilters
    const matchesMinRent = !filters.minRent || property.rent >= filters.minRent;
    const matchesMaxRent = !filters.maxRent || property.rent <= filters.maxRent;
    const matchesMinArea = !filters.minArea || property.area >= filters.minArea;
    const matchesMaxArea = !filters.maxArea || property.area <= filters.maxArea;
    const matchesBathrooms = !filters.bathrooms || property.bathrooms >= filters.bathrooms;
    const matchesAmenities = !filters.amenities?.length || 
      filters.amenities.some(amenity => property.amenities?.includes(amenity));
    const matchesAvailable = filters.available === undefined || property.available === filters.available;
    const matchesFeatured = filters.featured === undefined || property.featured === filters.featured;
    
    return matchesSearch && matchesBedrooms && matchesCounty && 
           matchesMinRent && matchesMaxRent && matchesMinArea && matchesMaxArea &&
           matchesBathrooms && matchesAmenities && matchesAvailable && matchesFeatured;
  });

  const sortedProperties = [...filteredProperties].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.rent - b.rent;
      case "price-high":
        return b.rent - a.rent;
      case "bedrooms":
        return b.bedrooms - a.bedrooms;
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    }
  });

  const featuredProperties = sortedProperties.filter(p => p.featured);
  const regularProperties = sortedProperties.filter(p => !p.featured);

  return (
    <section className="py-8 sm:py-12 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-6 sm:mb-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-foreground mb-2 sm:mb-4">
            Available Properties
          </h2>
          <p className="text-muted-foreground text-base sm:text-lg max-w-2xl mx-auto px-4">
            Browse our carefully curated selection of rental properties across Kenya
          </p>
        </div>

        {/* Filters */}
        <div className="bg-card rounded-xl p-4 sm:p-6 mb-6 sm:mb-8 shadow-sm">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3 sm:gap-4">
            <div className="relative sm:col-span-2 lg:col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search by title or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 text-sm"
              />
            </div>
            
            <Select value={filterCounty} onValueChange={setFilterCounty}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Counties" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Counties</SelectItem>
                {counties.map(county => (
                  <SelectItem key={county} value={county}>{county}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filterBedrooms} onValueChange={setFilterBedrooms}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Any Bedrooms" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any Bedrooms</SelectItem>
                <SelectItem value="1">1 Bedroom</SelectItem>
                <SelectItem value="2">2 Bedrooms</SelectItem>
                <SelectItem value="3">3 Bedrooms</SelectItem>
                <SelectItem value="4">4+ Bedrooms</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
                <SelectItem value="bedrooms">Most Bedrooms</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Featured Properties */}
        {featuredProperties.length > 0 && (
          <div className="mb-8 sm:mb-12">
            <h3 className="text-xl sm:text-2xl font-semibold text-foreground mb-4 sm:mb-6 flex items-center">
              <SlidersHorizontal className="h-5 w-5 sm:h-6 sm:w-6 mr-2 text-primary" />
              Featured Properties
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {featuredProperties.map((property) => (
                <div key={property.id} className="relative">
                  <PropertyCard
                    property={property}
                    onViewDetails={onViewDetails}
                  />
                  {onAddToCompare && (
                    <div className="absolute top-2 right-2">
                      {compareProperties.find(p => p.id === property.id) ? (
                        <Badge className="bg-blue-600">
                          ✓ In Compare
                        </Badge>
                      ) : (
                        <Button
                          variant="secondary"
                          size="sm"
                          className="h-6 px-2 text-xs"
                          onClick={() => onAddToCompare(property)}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Compare
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Regular Properties */}
        <div>
          <h3 className="text-xl sm:text-2xl font-semibold text-foreground mb-4 sm:mb-6">
            All Properties ({sortedProperties.length} found)
          </h3>
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-card rounded-lg p-4 animate-pulse">
                  <div className="bg-muted h-48 rounded-lg mb-4"></div>
                  <div className="bg-muted h-4 rounded mb-2"></div>
                  <div className="bg-muted h-4 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {regularProperties.map((property) => (
                <div key={property.id} className="relative">
                  <PropertyCard
                    property={property}
                    onViewDetails={onViewDetails}
                  />
                  {onAddToCompare && (
                    <div className="absolute top-2 right-2">
                      {compareProperties.find(p => p.id === property.id) ? (
                        <Badge className="bg-blue-600">
                          ✓ In Compare
                        </Badge>
                      ) : (
                        <Button
                          variant="secondary"
                          size="sm"
                          className="h-6 px-2 text-xs"
                          onClick={() => onAddToCompare(property)}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Compare
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {sortedProperties.length === 0 && !loading && (
          <div className="text-center py-8 sm:py-12">
            <p className="text-muted-foreground text-base sm:text-lg px-4">
              No properties found matching your criteria. Try adjusting your filters.
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default PropertyGrid;