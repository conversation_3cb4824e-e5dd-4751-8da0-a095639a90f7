import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Flag, Send, AlertTriangle } from 'lucide-react';
import { PropertyWithOwner } from '@/types/property';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';

interface ReportIssueProps {
  property: PropertyWithOwner;
  trigger?: React.ReactNode;
}

export const ReportIssue: React.FC<ReportIssueProps> = ({ property, trigger }) => {
  const [open, setOpen] = useState(false);
  const [issueType, setIssueType] = useState('');
  const [description, setDescription] = useState('');
  const [reporterEmail, setReporterEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const issueTypes = [
    { value: 'fraud', label: 'Fraudulent Listing' },
    { value: 'misinformation', label: 'Incorrect Information' },
    { value: 'images', label: 'Inappropriate Images' },
    { value: 'spam', label: 'Spam/Duplicate Listing' },
    { value: 'discrimination', label: 'Discriminatory Content' },
    { value: 'unavailable', label: 'Property No Longer Available' },
    { value: 'pricing', label: 'Pricing Issues' },
    { value: 'other', label: 'Other' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!issueType || !description.trim()) {
      toast({
        title: "Missing Information",
        description: "Please select an issue type and provide a description.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Here you would typically send the report to your backend
      // For now, we'll simulate the submission
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create the report data
      const reportData = {
        propertyId: property.id,
        propertyTitle: property.title,
        issueType,
        description,
        reporterEmail: reporterEmail || user?.email || 'anonymous',
        reportedAt: new Date().toISOString(),
        propertyLocation: property.location,
        propertyOwner: property.profiles?.full_name || 'Unknown'
      };

      console.log('Report submitted:', reportData);

      toast({
        title: "Report Submitted",
        description: "Thank you for reporting this issue. We'll review it within 24 hours.",
      });

      // Reset form
      setIssueType('');
      setDescription('');
      setReporterEmail('');
      setOpen(false);

    } catch (error) {
      console.error('Error submitting report:', error);
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your report. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" className="w-full">
      <Flag className="h-4 w-4 mr-2" />
      Report Issue
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Flag className="h-5 w-5 text-orange-500" />
            Report Issue
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="issue-type">Issue Type *</Label>
            <Select value={issueType} onValueChange={setIssueType} required>
              <SelectTrigger>
                <SelectValue placeholder="Select an issue type" />
              </SelectTrigger>
              <SelectContent>
                {issueTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              placeholder="Please describe the issue in detail..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              required
              rows={4}
            />
          </div>

          {!user && (
            <div className="space-y-2">
              <Label htmlFor="reporter-email">Your Email (optional)</Label>
              <Input
                id="reporter-email"
                type="email"
                placeholder="<EMAIL>"
                value={reporterEmail}
                onChange={(e) => setReporterEmail(e.target.value)}
              />
            </div>
          )}

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-sm">
              Reports are reviewed by our moderation team. False reports may result in account restrictions.
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Property Details:</h4>
            <div className="text-xs text-muted-foreground space-y-1 bg-muted p-3 rounded">
              <p><strong>Title:</strong> {property.title}</p>
              <p><strong>Location:</strong> {property.location}</p>
              <p><strong>Rent:</strong> KSH {property.rent.toLocaleString()}</p>
              <p><strong>Property ID:</strong> {property.id}</p>
            </div>
          </div>

          <div className="flex gap-2 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? (
                "Submitting..."
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Submit Report
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
