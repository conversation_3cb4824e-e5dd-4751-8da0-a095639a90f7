import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Bell, BellOff, Search, X, Plus, Edit, Trash2 } from 'lucide-react';
import { SearchFilters } from '@/types/property';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

interface SavedSearch {
  id: string;
  name: string;
  filters: SearchFilters;
  alertsEnabled: boolean;
  createdAt: string;
  lastChecked?: string;
  newPropertiesCount?: number;
}

interface SavedSearchesProps {
  currentFilters: SearchFilters;
  onLoadSearch: (filters: SearchFilters) => void;
}

export const SavedSearches: React.FC<SavedSearchesProps> = ({
  currentFilters,
  onLoadSearch
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([
    // Mock data - in real app, fetch from backend
    {
      id: '1',
      name: 'Westlands 2BR under 80k',
      filters: {
        location: 'Westlands',
        county: 'Nairobi',
        bedrooms: 2,
        maxRent: 80000
      },
      alertsEnabled: true,
      createdAt: '2024-01-15',
      newPropertiesCount: 3
    },
    {
      id: '2',
      name: 'Karen Houses with Garden',
      filters: {
        location: 'Karen',
        county: 'Nairobi',
        amenities: ['Garden', 'Parking']
      },
      alertsEnabled: false,
      createdAt: '2024-01-10'
    }
  ]);
  
  const [showSaveForm, setShowSaveForm] = useState(false);
  const [searchName, setSearchName] = useState('');
  const [enableAlerts, setEnableAlerts] = useState(true);

  const hasActiveFilters = () => {
    return Object.values(currentFilters).some(value => 
      value !== undefined && value !== null && value !== '' && 
      (Array.isArray(value) ? value.length > 0 : true)
    );
  };

  const getFilterSummary = (filters: SearchFilters) => {
    const summary = [];
    if (filters.county) summary.push(filters.county);
    if (filters.location) summary.push(filters.location);
    if (filters.bedrooms) summary.push(`${filters.bedrooms} bed${filters.bedrooms > 1 ? 's' : ''}`);
    if (filters.maxRent) summary.push(`Under KSH ${filters.maxRent.toLocaleString()}`);
    if (filters.amenities?.length) summary.push(`${filters.amenities.length} amenities`);
    return summary.slice(0, 3).join(', ') + (summary.length > 3 ? '...' : '');
  };

  const saveCurrentSearch = () => {
    if (!searchName.trim()) {
      toast({
        title: "Search name required",
        description: "Please enter a name for your saved search",
        variant: "destructive"
      });
      return;
    }

    const newSearch: SavedSearch = {
      id: Date.now().toString(),
      name: searchName,
      filters: currentFilters,
      alertsEnabled: enableAlerts,
      createdAt: new Date().toISOString().split('T')[0]
    };

    setSavedSearches(prev => [...prev, newSearch]);
    setShowSaveForm(false);
    setSearchName('');
    
    toast({
      title: "Search saved!",
      description: `"${searchName}" has been saved ${enableAlerts ? 'with alerts enabled' : ''}`,
    });
  };

  const deleteSearch = (id: string) => {
    setSavedSearches(prev => prev.filter(search => search.id !== id));
    toast({
      title: "Search deleted",
      description: "Your saved search has been removed",
    });
  };

  const toggleAlerts = (id: string) => {
    setSavedSearches(prev => prev.map(search => 
      search.id === id 
        ? { ...search, alertsEnabled: !search.alertsEnabled }
        : search
    ));
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Bell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Save Your Searches</h3>
          <p className="text-muted-foreground mb-4">
            Sign in to save searches and get notified when new properties match your criteria
          </p>
          <Button>Sign In</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Saved Searches ({savedSearches.length})
          </CardTitle>
          
          {hasActiveFilters() && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSaveForm(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Save Current
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Save Current Search Form */}
        {showSaveForm && (
          <div className="p-4 border rounded-lg bg-muted/30 space-y-3">
            <div className="space-y-2">
              <Label>Search Name</Label>
              <Input
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                placeholder="e.g., Westlands 2BR under 80k"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="enable-alerts"
                checked={enableAlerts}
                onCheckedChange={setEnableAlerts}
              />
              <Label htmlFor="enable-alerts" className="text-sm">
                Get email alerts for new properties
              </Label>
            </div>
            
            <div className="text-sm text-muted-foreground">
              Current filters: {getFilterSummary(currentFilters)}
            </div>
            
            <div className="flex gap-2">
              <Button onClick={saveCurrentSearch} size="sm">
                Save Search
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSaveForm(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Saved Searches List */}
        {savedSearches.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No saved searches yet</p>
            <p className="text-sm">Save your current search to get started</p>
          </div>
        ) : (
          <div className="space-y-3">
            {savedSearches.map((search) => (
              <div key={search.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{search.name}</h4>
                      {search.newPropertiesCount && search.newPropertiesCount > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {search.newPropertiesCount} new
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {getFilterSummary(search.filters)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Saved {search.createdAt}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleAlerts(search.id)}
                      className="h-8 w-8 p-0"
                    >
                      {search.alertsEnabled ? (
                        <Bell className="h-4 w-4 text-green-600" />
                      ) : (
                        <BellOff className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteSearch(search.id)}
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onLoadSearch(search.filters)}
                    className="flex-1"
                  >
                    Load Search
                  </Button>
                  {search.newPropertiesCount && search.newPropertiesCount > 0 && (
                    <Button size="sm" className="flex-1">
                      View {search.newPropertiesCount} New
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Alert Settings Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <h5 className="font-medium text-blue-900 mb-1">📧 Email Alerts</h5>
          <p className="text-sm text-blue-800">
            Get notified daily when new properties match your saved searches. 
            You can enable/disable alerts for each search individually.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
