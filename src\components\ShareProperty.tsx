import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, Di<PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Share2, Copy, Facebook, Twitter, MessageCircle, Mail, Check } from 'lucide-react';
import { PropertyWithOwner } from '@/types/property';
import { useToast } from '@/hooks/use-toast';

interface SharePropertyProps {
  property: PropertyWithOwner;
  trigger?: React.ReactNode;
}

export const ShareProperty: React.FC<SharePropertyProps> = ({ property, trigger }) => {
  const [copied, setCopied] = useState(false);
  const [open, setOpen] = useState(false);
  const { toast } = useToast();

  const propertyUrl = `${window.location.origin}/property/${property.id}`;
  const shareText = `Check out this property: ${property.title} in ${property.location} for KSH ${property.rent.toLocaleString()}/month`;

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(propertyUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast({
        title: "Link copied!",
        description: "Property link has been copied to clipboard",
      });
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const shareViaWhatsApp = () => {
    const whatsappText = encodeURIComponent(`${shareText}\n\n${propertyUrl}`);
    window.open(`https://wa.me/?text=${whatsappText}`, '_blank');
  };

  const shareViaEmail = () => {
    const subject = encodeURIComponent(`Property Recommendation: ${property.title}`);
    const body = encodeURIComponent(`Hi,\n\nI found this property that might interest you:\n\n${shareText}\n\nView details: ${propertyUrl}\n\nBest regards`);
    window.open(`mailto:?subject=${subject}&body=${body}`, '_self');
  };

  const shareViaFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(propertyUrl)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
  };

  const shareViaTwitter = () => {
    const twitterText = encodeURIComponent(`${shareText} ${propertyUrl}`);
    const twitterUrl = `https://twitter.com/intent/tweet?text=${twitterText}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
  };

  const ShareContent = () => (
    <div className="space-y-4">
      {/* Copy Link */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">Property Link</label>
        <div className="flex gap-2">
          <Input
            value={propertyUrl}
            readOnly
            className="text-sm"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={copyToClipboard}
            className="flex items-center gap-2"
          >
            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            {copied ? 'Copied!' : 'Copy'}
          </Button>
        </div>
      </div>

      {/* Share Buttons */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-foreground">Share via</label>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          <Button
            variant="outline"
            onClick={shareViaWhatsApp}
            className="justify-start bg-green-50 hover:bg-green-100 border-green-200 text-green-800 hover:text-green-900 dark:bg-green-900/20 dark:hover:bg-green-800/30 dark:border-green-700 dark:text-green-300 dark:hover:text-green-200 text-sm"
          >
            <MessageCircle className="h-4 w-4 mr-2 flex-shrink-0 text-green-700 dark:text-green-400" />
            <span className="truncate">WhatsApp</span>
          </Button>

          <Button
            variant="outline"
            onClick={shareViaEmail}
            className="justify-start text-sm"
          >
            <Mail className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="truncate">Email</span>
          </Button>

          <Button
            variant="outline"
            onClick={shareViaFacebook}
            className="justify-start bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-800 hover:text-blue-900 dark:bg-blue-900/20 dark:hover:bg-blue-800/30 dark:border-blue-700 dark:text-blue-300 dark:hover:text-blue-200 text-sm"
          >
            <Facebook className="h-4 w-4 mr-2 flex-shrink-0 text-blue-700 dark:text-blue-400" />
            <span className="truncate">Facebook</span>
          </Button>

          <Button
            variant="outline"
            onClick={shareViaTwitter}
            className="justify-start bg-sky-50 hover:bg-sky-100 border-sky-200 text-sky-800 hover:text-sky-900 dark:bg-sky-900/20 dark:hover:bg-sky-800/30 dark:border-sky-700 dark:text-sky-300 dark:hover:text-sky-200 text-sm"
          >
            <Twitter className="h-4 w-4 mr-2 flex-shrink-0 text-sky-700 dark:text-sky-400" />
            <span className="truncate">Twitter</span>
          </Button>
        </div>
      </div>

      {/* Share Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <h5 className="font-medium text-blue-900 mb-1">💡 Sharing Tips</h5>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Share with friends and family looking for rentals</li>
          <li>• Property owners love when their listings get shared</li>
          <li>• Help others find their perfect home!</li>
        </ul>
      </div>
    </div>
  );

  // If a trigger is provided, render as a dialog
  if (trigger) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Share2 className="h-5 w-5" />
              Share This Property
            </DialogTitle>
          </DialogHeader>
          <ShareContent />
        </DialogContent>
      </Dialog>
    );
  }

  // Default card mode
  return (
    <Card>
      <CardHeader className="p-4 sm:p-6">
        <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
          <Share2 className="h-5 w-5" />
          Share This Property
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 p-4 sm:p-6">
        <ShareContent />
      </CardContent>
    </Card>
  );
};
