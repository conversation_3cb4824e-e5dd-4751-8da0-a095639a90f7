import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock,
  Flag,
  Home,
  TrendingUp,
  Eye,
  Settings
} from 'lucide-react';
import { useAdminVerification } from '@/hooks/useAdminVerification';
import { VerificationStats } from '@/types/verification';
import { VerificationReview } from './VerificationReview';
import { FraudReportManagement } from './FraudReportManagement';
import { LandlordManagement } from './LandlordManagement';
import { DocumentUrlDebug } from './DocumentUrlDebug';

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  description: string;
  trend?: 'up' | 'down' | 'stable';
  color?: 'default' | 'green' | 'red' | 'yellow';
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, description, trend, color = 'default' }) => {
  const colorClasses = {
    default: 'text-primary',
    green: 'text-green-500 dark:text-green-400',
    red: 'text-destructive',
    yellow: 'text-yellow-500 dark:text-yellow-400'
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          </div>
          <div className={`${colorClasses[color]}`}>
            {icon}
          </div>
        </div>
        {trend && (
          <div className="mt-2 flex items-center">
            <TrendingUp className={`h-3 w-3 mr-1 ${
              trend === 'up' ? 'text-green-500 dark:text-green-400' : 
              trend === 'down' ? 'text-destructive' : 'text-muted-foreground'
            }`} />
            <span className="text-xs text-muted-foreground">
              {trend === 'up' ? 'Increasing' : trend === 'down' ? 'Decreasing' : 'Stable'}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

interface AdminVerificationDashboardProps {
  initialTab?: string;
}

export const AdminVerificationDashboard: React.FC<AdminVerificationDashboardProps> = ({ initialTab = 'overview' }) => {
  const { 
    loading, 
    stats, 
    isAdmin, 
    getVerificationStats,
    getPendingVerifications,
    getFraudReports 
  } = useAdminVerification();
  
  const [adminStatus, setAdminStatus] = useState<boolean | null>(null);
  const [activeTab, setActiveTab] = useState(initialTab);
  const [showDebugTool, setShowDebugTool] = useState(false);

  useEffect(() => {
    const checkAdmin = async () => {
      const status = await isAdmin();
      setAdminStatus(status);
      if (status) {
        await getVerificationStats();
      }
    };

    checkAdmin();
  }, [isAdmin, getVerificationStats]);

  if (adminStatus === null) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-pulse text-center">
          <Shield className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
          <p className="text-muted-foreground">Checking permissions...</p>
        </div>
      </div>
    );
  }

  if (!adminStatus) {
    return (
      <Card className="max-w-md mx-auto mt-8">
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-destructive" />
          <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
          <p className="text-muted-foreground">
            You don't have permission to access the admin verification dashboard.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                  <div className="h-8 bg-muted rounded w-1/3"></div>
                  <div className="h-3 bg-muted rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Verification Dashboard</h1>
          <p className="text-muted-foreground">
            Manage landlord verifications and maintain platform security
          </p>
        </div>
        <Button onClick={() => getVerificationStats()} disabled={loading}>
          <Shield className="h-4 w-4 mr-2" />
          Refresh Stats
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Landlords"
          value={stats.total_landlords}
          icon={<Users className="h-8 w-8" />}
          description="Registered property owners"
          color="default"
        />
        
        <StatCard
          title="Pending Reviews"
          value={stats.pending_verifications}
          icon={<Clock className="h-8 w-8" />}
          description="Awaiting verification"
          color="yellow"
        />
        
        <StatCard
          title="Verified Landlords"
          value={stats.verified_landlords}
          icon={<CheckCircle className="h-8 w-8" />}
          description="Successfully verified"
          color="green"
        />
        
        <StatCard
          title="Fraud Reports"
          value={stats.open_fraud_reports}
          icon={<Flag className="h-8 w-8" />}
          description="Active investigations"
          color="red"
        />
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatCard
          title="Rejected Applications"
          value={stats.rejected_applications}
          icon={<XCircle className="h-8 w-8" />}
          description="Failed verification"
          color="red"
        />
        
        <StatCard
          title="Flagged Accounts"
          value={stats.flagged_accounts}
          icon={<AlertTriangle className="h-8 w-8" />}
          description="Suspended for review"
          color="yellow"
        />
        
        <StatCard
          title="Properties Pending"
          value={stats.properties_pending_approval}
          icon={<Home className="h-8 w-8" />}
          description="Awaiting approval"
          color="yellow"
        />
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Verification Performance</CardTitle>
            <CardDescription>Key performance indicators</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Average Processing Time</span>
              <Badge variant="outline">{stats.average_verification_time_days} days</Badge>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Verification Success Rate</span>
              <Badge variant="outline" className="text-green-500 dark:text-green-400">
                {stats.total_landlords > 0 
                  ? Math.round((stats.verified_landlords / stats.total_landlords) * 100)
                  : 0}%
              </Badge>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Fraud Detection Rate</span>
              <Badge variant="outline" className="text-destructive">
                {stats.total_landlords > 0 
                  ? Math.round((stats.open_fraud_reports / stats.total_landlords) * 100)
                  : 0}%
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Health</CardTitle>
            <CardDescription>Platform security status</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Security Status Indicators */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Verification System</span>
                <Badge variant="outline" className="text-green-500 dark:text-green-400">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Operational
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Fraud Detection</span>
                <Badge variant="outline" className="text-green-500 dark:text-green-400">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Active
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Document Storage</span>
                <Badge variant="outline" className="text-green-500 dark:text-green-400">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Secure
                </Badge>
              </div>
            </div>

            {/* Alerts */}
            {stats.pending_verifications > 10 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  High volume of pending verifications. Consider adding more reviewers.
                </AlertDescription>
              </Alert>
            )}

            {stats.open_fraud_reports > 5 && (
              <Alert variant="destructive">
                <Flag className="h-4 w-4" />
                <AlertDescription>
                  Multiple active fraud reports require immediate attention.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common administrative tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => setActiveTab('landlords')}
            >
              <Users className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium">Manage Landlords</div>
                <div className="text-sm text-muted-foreground">
                  {stats.total_landlords} total
                </div>
              </div>
            </Button>

            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => setActiveTab('verifications')}
            >
              <Clock className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium">Review Verifications</div>
                <div className="text-sm text-muted-foreground">
                  {stats.pending_verifications} pending
                </div>
              </div>
            </Button>

            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => setActiveTab('fraud-reports')}
            >
              <Flag className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium">Fraud Reports</div>
                <div className="text-sm text-muted-foreground">
                  {stats.open_fraud_reports} open
                </div>
              </div>
            </Button>

            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => setActiveTab('properties')}
            >
              <Home className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium">Property Reviews</div>
                <div className="text-sm text-muted-foreground">
                  {stats.properties_pending_approval} pending
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Navigation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="landlords">
            Landlords
            <Badge variant="secondary" className="ml-2 text-xs">
              {stats.total_landlords}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="verifications">
            Verifications
            {stats.pending_verifications > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {stats.pending_verifications}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="fraud-reports">
            Fraud Reports
            {stats.open_fraud_reports > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {stats.open_fraud_reports}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="properties">
            Properties
            {stats.properties_pending_approval > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {stats.properties_pending_approval}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardContent className="p-6">
              <div className="text-center space-y-4">
                <Eye className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Dashboard Overview</h3>
                <p className="text-muted-foreground">
                  Select a tab above to manage verifications, review fraud reports, or approve properties.
                </p>
                
                {/* Admin Tools */}
                <div className="pt-4 border-t">
                  <h4 className="text-md font-medium mb-3">Admin Tools</h4>
                  <Button 
                    variant="outline" 
                    onClick={() => setShowDebugTool(true)}
                    className="flex items-center gap-2"
                  >
                    <Settings className="h-4 w-4" />
                    Document URL Debug Tool
                  </Button>
                  <p className="text-sm text-muted-foreground mt-2">
                    Use this tool to diagnose and fix verification document display issues
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="landlords">
          <LandlordManagement />
        </TabsContent>

        <TabsContent value="verifications">
          <VerificationReview />
        </TabsContent>

        <TabsContent value="fraud-reports">
          <FraudReportManagement />
        </TabsContent>

        <TabsContent value="properties">
          {/* Property management will be implemented in the next component */}
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <Home className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Property Management</h3>
                <p className="text-muted-foreground">
                  Property approval interface will be loaded here.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Debug Tool Modal */}
      <DocumentUrlDebug 
        isVisible={showDebugTool}
        onClose={() => setShowDebugTool(false)}
      />
    </div>
  );
};
