import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  Eye,
  Info
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { regenerateDocumentUrl, validateDocumentUrl } from '@/lib/documentUtils';
import { VerificationDocument } from '@/types/verification';

interface DocumentUrlDebugProps {
  isVisible: boolean;
  onClose: () => void;
}

export const DocumentUrlDebug: React.FC<DocumentUrlDebugProps> = ({ isVisible, onClose }) => {
  const [documents, setDocuments] = useState<VerificationDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState<string | null>(null);
  const [results, setResults] = useState<Record<string, { valid: boolean; regenerated?: string }>>({});

  const fetchAllDocuments = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('verification_documents')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50); // Limit to prevent overwhelming

      if (error) throw error;
      setDocuments(data || []);
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const testDocumentUrl = async (document: VerificationDocument) => {
    setTesting(document.id);
    try {
      const isValid = await validateDocumentUrl(document.document_url);
      let regeneratedUrl = '';
      
      if (!isValid) {
        regeneratedUrl = regenerateDocumentUrl(document.document_url);
      }

      setResults(prev => ({
        ...prev,
        [document.id]: {
          valid: isValid,
          regenerated: regeneratedUrl
        }
      }));
    } catch (error) {
      console.error('Error testing URL:', error);
      setResults(prev => ({
        ...prev,
        [document.id]: {
          valid: false,
          regenerated: regenerateDocumentUrl(document.document_url)
        }
      }));
    } finally {
      setTesting(null);
    }
  };

  const testAllUrls = async () => {
    for (const document of documents) {
      await testDocumentUrl(document);
      // Add small delay to prevent overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  };

  const fixDocumentUrl = async (document: VerificationDocument) => {
    const result = results[document.id];
    if (!result?.regenerated) return;

    try {
      const { error } = await supabase
        .from('verification_documents')
        .update({ document_url: result.regenerated })
        .eq('id', document.id);

      if (error) throw error;

      // Update local state
      setDocuments(prev => prev.map(doc => 
        doc.id === document.id 
          ? { ...doc, document_url: result.regenerated }
          : doc
      ));

      // Update results
      setResults(prev => ({
        ...prev,
        [document.id]: {
          valid: true,
          regenerated: undefined
        }
      }));

      console.log('Fixed URL for document:', document.id);
    } catch (error) {
      console.error('Error fixing URL:', error);
    }
  };

  useEffect(() => {
    if (isVisible) {
      fetchAllDocuments();
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document URL Debug Tool
              </CardTitle>
              <CardDescription>
                Test and fix verification document URLs that are not displaying properly
              </CardDescription>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              This tool helps diagnose and fix issues with verification document URLs that appear as "crack images" 
              in the admin interface. It tests URL accessibility and can regenerate broken URLs.
            </AlertDescription>
          </Alert>

          <div className="flex gap-3">
            <Button onClick={fetchAllDocuments} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh Documents
            </Button>
            <Button onClick={testAllUrls} disabled={loading || documents.length === 0}>
              Test All URLs
            </Button>
          </div>

          {documents.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-muted-foreground">No documents found</p>
            </div>
          ) : (
            <div className="space-y-3">
              {documents.map((document) => {
                const result = results[document.id];
                const isLoading = testing === document.id;
                
                return (
                  <Card key={document.id} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium capitalize">
                            {document.document_type.replace('_', ' ')} - {document.document_name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            User: {document.user_id} | Created: {new Date(document.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          {result && (
                            <Badge variant={result.valid ? "default" : "destructive"}>
                              {result.valid ? "Valid" : "Broken"}
                            </Badge>
                          )}
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => testDocumentUrl(document)}
                            disabled={isLoading}
                          >
                            {isLoading ? (
                              <RefreshCw className="h-3 w-3 animate-spin" />
                            ) : (
                              <Eye className="h-3 w-3" />
                            )}
                            Test
                          </Button>
                        </div>
                      </div>

                      <div className="text-xs break-all bg-gray-100 p-2 rounded">
                        <strong>Current URL:</strong> {document.document_url}
                      </div>

                      {result && !result.valid && result.regenerated && (
                        <div className="space-y-2">
                          <div className="text-xs break-all bg-green-50 p-2 rounded border border-green-200">
                            <strong>Regenerated URL:</strong> {result.regenerated}
                          </div>
                          <div className="flex gap-2">
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => window.open(result.regenerated, '_blank')}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              Test New URL
                            </Button>
                            <Button 
                              size="sm" 
                              onClick={() => fixDocumentUrl(document)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Fix URL
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
