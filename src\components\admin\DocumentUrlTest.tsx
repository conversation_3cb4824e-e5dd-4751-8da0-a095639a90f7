import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  Eye,
  Info,
  TestTube
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { getDocumentUrl, regenerateDocumentUrl, validateDocumentUrl } from '@/lib/documentUtils';

interface DocumentUrlTestProps {
  isVisible: boolean;
  onClose: () => void;
}

export const DocumentUrlTest: React.FC<DocumentUrlTestProps> = ({ isVisible, onClose }) => {
  const [testUrl, setTestUrl] = useState('');
  const [testResult, setTestResult] = useState<{
    original: string;
    signedUrl: string;
    isValid: boolean;
    error?: string;
  } | null>(null);
  const [testing, setTesting] = useState(false);

  const testSignedUrlGeneration = async () => {
    if (!testUrl.trim()) return;

    setTesting(true);
    setTestResult(null);

    try {
      console.log('Testing URL:', testUrl);
      
      // Generate signed URL
      const signedUrl = await getDocumentUrl(testUrl);
      console.log('Generated signed URL:', signedUrl);
      
      // Validate the signed URL
      const isValid = await validateDocumentUrl(signedUrl);
      console.log('URL validation result:', isValid);

      setTestResult({
        original: testUrl,
        signedUrl,
        isValid,
      });
    } catch (error) {
      console.error('Error testing URL:', error);
      setTestResult({
        original: testUrl,
        signedUrl: '',
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setTesting(false);
    }
  };

  const testStorageAccess = async () => {
    setTesting(true);
    try {
      // Test basic storage access
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
      console.log('Available buckets:', buckets, bucketsError);

      // Test verification-documents bucket access
      const { data: files, error: filesError } = await supabase.storage
        .from('verification-documents')
        .list('', { limit: 5 });
      
      console.log('Files in verification-documents bucket:', files, filesError);
      
      if (files && files.length > 0) {
        // Test signed URL generation for first file
        const firstFile = files[0];
        const { data: signedData, error: signedError } = await supabase.storage
          .from('verification-documents')
          .createSignedUrl(firstFile.name, 3600);
        
        console.log('Test signed URL for first file:', signedData, signedError);
      }
    } catch (error) {
      console.error('Storage access test error:', error);
    } finally {
      setTesting(false);
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TestTube className="h-5 w-5" />
                Document URL Testing Tool
              </CardTitle>
              <CardDescription>
                Test signed URL generation for verification documents
              </CardDescription>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              This tool helps test the new signed URL generation for private verification documents.
              Enter a document URL or file path to test signed URL generation.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <div>
              <Label htmlFor="testUrl">Document URL or File Path</Label>
              <Input
                id="testUrl"
                value={testUrl}
                onChange={(e) => setTestUrl(e.target.value)}
                placeholder="e.g., user-id/national_id_123456.jpg or full URL"
                className="mt-1"
              />
            </div>

            <div className="flex gap-3">
              <Button 
                onClick={testSignedUrlGeneration} 
                disabled={testing || !testUrl.trim()}
              >
                <TestTube className={`h-4 w-4 mr-2 ${testing ? 'animate-spin' : ''}`} />
                Test Signed URL
              </Button>
              <Button 
                variant="outline"
                onClick={testStorageAccess} 
                disabled={testing}
              >
                <Eye className="h-4 w-4 mr-2" />
                Test Storage Access
              </Button>
            </div>
          </div>

          {testResult && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  {testResult.isValid ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                  Test Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Original URL/Path</Label>
                  <p className="text-sm break-all bg-gray-100 p-2 rounded">
                    {testResult.original}
                  </p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium">Generated Signed URL</Label>
                  <p className="text-sm break-all bg-gray-100 p-2 rounded">
                    {testResult.signedUrl || 'Failed to generate'}
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-medium">Validation Status</Label>
                  <div className="flex items-center gap-2">
                    <Badge variant={testResult.isValid ? 'default' : 'destructive'}>
                      {testResult.isValid ? 'Valid' : 'Invalid'}
                    </Badge>
                    {testResult.error && (
                      <span className="text-sm text-red-600">{testResult.error}</span>
                    )}
                  </div>
                </div>

                {testResult.signedUrl && (
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.open(testResult.signedUrl, '_blank')}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Open in New Tab
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
