import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Flag, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Eye, 
  User,
  Home,
  Mail,
  Phone,
  Calendar,
  FileText,
  ExternalLink
} from 'lucide-react';
import { useAdminVerification } from '@/hooks/useAdminVerification';
import { FraudReport } from '@/types/verification';

interface FraudReportDetailsProps {
  report: FraudReport & {
    profiles?: { full_name: string; phone: string };
    properties?: { title: string; location: string };
  };
  onStatusUpdate: (reportId: string, status: string, notes?: string) => void;
}

const FraudReportDetails: React.FC<FraudReportDetailsProps> = ({ report, onStatusUpdate }) => {
  const [open, setOpen] = useState(false);
  const [newStatus, setNewStatus] = useState(report.status);
  const [resolutionNotes, setResolutionNotes] = useState(report.resolution_notes || '');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-red-600 bg-red-100';
      case 'investigating': return 'text-yellow-600 bg-yellow-100';
      case 'resolved': return 'text-green-600 bg-green-100';
      case 'dismissed': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleStatusUpdate = () => {
    onStatusUpdate(report.id, newStatus, resolutionNotes);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Flag className="h-4 w-4 text-red-600" />
                  <span className="font-medium capitalize">
                    {report.report_type.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getPriorityColor(report.priority)}>
                    {report.priority}
                  </Badge>
                  <Badge className={getStatusColor(report.status)}>
                    {report.status}
                  </Badge>
                </div>
              </div>
              
              <div className="text-sm text-muted-foreground">
                <div className="flex items-center gap-1 mb-1">
                  <User className="h-3 w-3" />
                  Reported: {report.profiles?.full_name || 'Unknown User'}
                </div>
                {report.properties && (
                  <div className="flex items-center gap-1 mb-1">
                    <Home className="h-3 w-3" />
                    Property: {report.properties.title}
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {new Date(report.created_at).toLocaleDateString()}
                </div>
              </div>
              
              <p className="text-sm line-clamp-2">{report.description}</p>
            </div>
          </CardContent>
        </Card>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Flag className="h-5 w-5 text-red-600" />
            Fraud Report Details
          </DialogTitle>
          <DialogDescription>
            Review and manage this fraud report
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Report Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Report Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Report Type</Label>
                  <p className="capitalize">{report.report_type.replace('_', ' ')}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Priority</Label>
                  <Badge className={getPriorityColor(report.priority)}>
                    {report.priority}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <Badge className={getStatusColor(report.status)}>
                    {report.status}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">Reported Date</Label>
                  <p>{new Date(report.created_at).toLocaleDateString()}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Description</Label>
                <div className="p-3 bg-gray-50 border rounded-lg mt-1">
                  <p className="text-sm whitespace-pre-wrap">{report.description}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Reporter Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Reporter Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{report.reporter_email}</span>
                </div>
                {report.reporter_phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{report.reporter_phone}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Reported User/Property */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Reported Subject</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Landlord:</span>
                  <span>{report.profiles?.full_name || 'Unknown User'}</span>
                </div>
                {report.profiles?.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{report.profiles.phone}</span>
                  </div>
                )}
                {report.properties && (
                  <>
                    <div className="flex items-center gap-2">
                      <Home className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Property:</span>
                      <span>{report.properties.title}</span>
                    </div>
                    <div className="text-sm text-muted-foreground ml-6">
                      {report.properties.location}
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Evidence */}
          {report.evidence_urls && report.evidence_urls.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Evidence</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {report.evidence_urls.map((url, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">Evidence {index + 1}</span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(url, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Status Update */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Update Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="status-select">New Status</Label>
                <Select value={newStatus} onValueChange={setNewStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="investigating">Investigating</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="dismissed">Dismissed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="resolution-notes">Resolution Notes</Label>
                <Textarea
                  id="resolution-notes"
                  placeholder="Add notes about the investigation or resolution..."
                  value={resolutionNotes}
                  onChange={(e) => setResolutionNotes(e.target.value)}
                  rows={4}
                />
              </div>

              <div className="flex gap-3">
                <Button onClick={handleStatusUpdate}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Update Status
                </Button>
                <Button variant="outline" onClick={() => setOpen(false)}>
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Previous Resolution */}
          {report.status !== 'open' && report.resolution_notes && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Previous Resolution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-3 bg-gray-50 border rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{report.resolution_notes}</p>
                  {report.resolved_at && (
                    <p className="text-xs text-muted-foreground mt-2">
                      Resolved on {new Date(report.resolved_at).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const FraudReportManagement: React.FC = () => {
  const { getFraudReports, updateFraudReportStatus, loading } = useAdminVerification();
  const [fraudReports, setFraudReports] = useState<any[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    loadFraudReports();
  }, [statusFilter]);

  const loadFraudReports = async () => {
    const reports = await getFraudReports(statusFilter === 'all' ? undefined : statusFilter);
    setFraudReports(reports);
  };

  const handleStatusUpdate = async (reportId: string, status: string, notes?: string) => {
    const success = await updateFraudReportStatus(reportId, status, notes);
    if (success) {
      await loadFraudReports();
    }
  };

  const getStatusCounts = () => {
    return {
      all: fraudReports.length,
      open: fraudReports.filter(r => r.status === 'open').length,
      investigating: fraudReports.filter(r => r.status === 'investigating').length,
      resolved: fraudReports.filter(r => r.status === 'resolved').length,
      dismissed: fraudReports.filter(r => r.status === 'dismissed').length,
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Fraud Report Management</h2>
          <p className="text-muted-foreground">
            Review and investigate fraud reports from users
          </p>
        </div>
        <Button onClick={loadFraudReports} disabled={loading}>
          <Flag className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Status Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <Label className="text-sm font-medium">Filter by Status:</Label>
            <div className="flex gap-2">
              {[
                { value: 'all', label: 'All', count: statusCounts.all },
                { value: 'open', label: 'Open', count: statusCounts.open },
                { value: 'investigating', label: 'Investigating', count: statusCounts.investigating },
                { value: 'resolved', label: 'Resolved', count: statusCounts.resolved },
                { value: 'dismissed', label: 'Dismissed', count: statusCounts.dismissed },
              ].map((filter) => (
                <Button
                  key={filter.value}
                  variant={statusFilter === filter.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setStatusFilter(filter.value)}
                >
                  {filter.label} ({filter.count})
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reports List */}
      <div className="space-y-4">
        {fraudReports.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
              <h3 className="text-lg font-semibold mb-2">No Fraud Reports</h3>
              <p className="text-muted-foreground">
                {statusFilter === 'all' 
                  ? 'No fraud reports have been submitted yet.'
                  : `No ${statusFilter} fraud reports found.`
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {fraudReports.map((report) => (
              <FraudReportDetails
                key={report.id}
                report={report}
                onStatusUpdate={handleStatusUpdate}
              />
            ))}
          </div>
        )}
      </div>

      {/* High Priority Alert */}
      {fraudReports.some(r => r.priority === 'critical' && r.status === 'open') && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Critical Priority Reports:</strong> There are critical priority fraud reports that require immediate attention.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
