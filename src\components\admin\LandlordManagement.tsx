import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  User, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  Star,
  Building,
  Phone,
  Mail,
  Calendar,
  AlertTriangle,
  Search,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';
import { useAdminVerification } from '@/hooks/useAdminVerification';
import { VerifiedProfile, VerificationDocument } from '@/types/verification';
import { regenerateDocumentUrl, getDocumentUrl } from '@/lib/documentUtils';

interface LandlordDetailsProps {
  landlord: VerifiedProfile;
  onClose: () => void;
  onStatusUpdate: () => void;
}

const LandlordDetails: React.FC<LandlordDetailsProps> = ({ landlord, onClose, onStatusUpdate }) => {
  const { getUserVerificationDocuments, reviewLandlordVerification } = useAdminVerification();
  const [documents, setDocuments] = useState<VerificationDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState<VerificationDocument | null>(null);
  const [documentUrl, setDocumentUrl] = useState<string>('');

  useEffect(() => {
    const loadDocuments = async () => {
      setLoading(true);
      try {
        const docs = await getUserVerificationDocuments(landlord.id);
        setDocuments(docs);
      } catch (error) {
        console.error('Error loading documents:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDocuments();
  }, [landlord.id, getUserVerificationDocuments]);

  // Generate signed URL when document is selected
  useEffect(() => {
    const generateDocumentUrl = async () => {
      if (selectedDocument) {
        try {
          const signedUrl = await getDocumentUrl(selectedDocument.document_url);
          setDocumentUrl(signedUrl);
        } catch (error) {
          console.error('Error generating document URL:', error);
          setDocumentUrl(selectedDocument.document_url); // Fallback to original URL
        }
      } else {
        setDocumentUrl('');
      }
    };

    generateDocumentUrl();
  }, [selectedDocument]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'bg-green-100 text-green-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified': return <CheckCircle className="h-4 w-4" />;
      case 'under_review': return <Clock className="h-4 w-4" />;
      case 'rejected': return <XCircle className="h-4 w-4" />;
      case 'suspended': return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const handleStatusChange = async (newStatus: 'pending' | 'under_review' | 'verified' | 'rejected' | 'suspended') => {
    try {
      const success = await reviewLandlordVerification(landlord.id, {
        verification_status: newStatus,
        admin_notes: `Status changed to ${newStatus} by admin`,
      });
      
      if (success) {
        onStatusUpdate();
        onClose();
      }
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  return (
    <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          {landlord.full_name} - Landlord Details
        </DialogTitle>
        <DialogDescription>
          Complete landlord profile and verification information
        </DialogDescription>
      </DialogHeader>

      <Tabs defaultValue="profile" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="documents">Documents ({documents.length})</TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-4">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Basic Information</span>
                <Badge className={getStatusColor(landlord.verification_status)}>
                  {getStatusIcon(landlord.verification_status)}
                  <span className="ml-1 capitalize">{landlord.verification_status}</span>
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Full Name</Label>
                <p className="text-sm">{landlord.full_name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Email</Label>
                <p className="text-sm">{landlord.id}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Phone</Label>
                <p className="text-sm">{landlord.phone || 'Not provided'}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">National ID</Label>
                <p className="text-sm">{landlord.national_id || 'Not provided'}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">KRA PIN</Label>
                <p className="text-sm">{landlord.tax_pin || 'Not provided'}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Trust Score</Label>
                <div className="flex items-center gap-2">
                  <span className="text-sm">{landlord.trust_score || 'N/A'}</span>
                  {landlord.trust_score && (
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-3 w-3 ${
                            star <= (landlord.trust_score || 0) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Business Information */}
          {(landlord.business_name || landlord.business_address || landlord.years_in_business) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Business Information
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {landlord.business_name && (
                  <div>
                    <Label className="text-sm font-medium">Business Name</Label>
                    <p className="text-sm">{landlord.business_name}</p>
                  </div>
                )}
                {landlord.years_in_business && (
                  <div>
                    <Label className="text-sm font-medium">Years in Business</Label>
                    <p className="text-sm">{landlord.years_in_business}</p>
                  </div>
                )}
                {landlord.business_address && (
                  <div className="md:col-span-2">
                    <Label className="text-sm font-medium">Business Address</Label>
                    <p className="text-sm">{landlord.business_address}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Verification History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Verification Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Account Created</p>
                  <p className="text-xs text-muted-foreground">
                    {new Date(landlord.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
              {landlord.verification_submitted_at && (
                <div className="flex items-center gap-3">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Verification Submitted</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(landlord.verification_submitted_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )}
              {landlord.verification_completed_at && (
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-sm font-medium">Verification Completed</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(landlord.verification_completed_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )}
              {landlord.admin_notes && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                  <p className="text-sm">
                    <strong>Admin Notes:</strong> {landlord.admin_notes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 mx-auto mb-2 text-muted-foreground animate-spin" />
              <p className="text-muted-foreground">Loading documents...</p>
            </div>
          ) : documents.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">No documents uploaded</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {documents.map((document) => (
                <Card key={document.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm font-medium capitalize">
                          {document.document_type.replace('_', ' ')}
                        </p>
                      </div>
                      <Badge variant={document.verification_status === 'approved' ? 'default' : 
                                   document.verification_status === 'rejected' ? 'destructive' : 'secondary'}>
                        {document.verification_status}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">{document.document_name}</p>
                    <p className="text-xs text-muted-foreground">
                      Uploaded: {new Date(document.created_at).toLocaleDateString()}
                    </p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full mt-3"
                      onClick={() => setSelectedDocument(document)}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Document Viewer Modal */}
          {selectedDocument && (
            <Dialog open={!!selectedDocument} onOpenChange={() => setSelectedDocument(null)}>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Document: {selectedDocument.document_name}</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4 bg-gray-50">
                    {documentUrl ? (
                      selectedDocument.mime_type?.startsWith('image/') ? (
                        <img
                          src={documentUrl}
                          alt="Document"
                          className="max-w-full h-auto rounded mx-auto"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const errorDiv = document.createElement('div');
                            errorDiv.innerHTML = `
                              <div class="text-center py-8">
                                <p class="text-red-600 font-medium">Failed to load document</p>
                                <button onclick="window.open('${documentUrl}', '_blank')"
                                        class="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                                  Try Direct View
                                </button>
                              </div>
                            `;
                            target.parentNode?.appendChild(errorDiv);
                          }}
                        />
                      ) : (
                        <div className="text-center py-8">
                          <FileText className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-muted-foreground mb-4">PDF Document</p>
                          <Button onClick={() => window.open(documentUrl, '_blank')}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Document
                          </Button>
                        </div>
                      )
                    ) : (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                        <p className="text-muted-foreground">Loading document...</p>
                      </div>
                    )}
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Admin Actions</CardTitle>
              <CardDescription>
                Change verification status or update landlord information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium mb-2 block">Change Verification Status</Label>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange('verified')}
                    disabled={landlord.verification_status === 'verified'}
                    className="bg-green-50 hover:bg-green-100 border-green-200 text-green-800 hover:text-green-900"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Verify
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange('under_review')}
                    disabled={landlord.verification_status === 'under_review'}
                    className="bg-yellow-50 hover:bg-yellow-100 border-yellow-200 text-yellow-800 hover:text-yellow-900"
                  >
                    <Clock className="h-4 w-4 mr-1" />
                    Under Review
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange('rejected')}
                    disabled={landlord.verification_status === 'rejected'}
                    className="bg-red-50 hover:bg-red-100 border-red-200 text-red-800 hover:text-red-900"
                  >
                    <XCircle className="h-4 w-4 mr-1" />
                    Reject
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange('suspended')}
                    disabled={landlord.verification_status === 'suspended'}
                    className="bg-red-50 hover:bg-red-100 border-red-200 text-red-800 hover:text-red-900"
                  >
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Suspend
                  </Button>
                </div>
              </div>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Warning:</strong> Changing the verification status will immediately affect 
                  the landlord's account and notify them of the change.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DialogContent>
  );
};

export const LandlordManagement: React.FC = () => {
  const { loading, getAllLandlords } = useAdminVerification();
  const [landlords, setLandlords] = useState<VerifiedProfile[]>([]);
  const [filteredLandlords, setFilteredLandlords] = useState<VerifiedProfile[]>([]);
  const [selectedLandlord, setSelectedLandlord] = useState<VerifiedProfile | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');

  const loadAllLandlords = useCallback(async () => {
    try {
      const allLandlords = await getAllLandlords();
      setLandlords(allLandlords);
      setFilteredLandlords(allLandlords);
    } catch (error) {
      console.error('Error loading landlords:', error);
    }
  }, [getAllLandlords]);

  useEffect(() => {
    loadAllLandlords();
  }, [loadAllLandlords]);

  useEffect(() => {
    const filtered = landlords.filter(landlord => {
      const matchesSearch = 
        landlord.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        landlord.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        landlord.phone?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        landlord.national_id?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || landlord.verification_status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.full_name.localeCompare(b.full_name);
        case 'status':
          return a.verification_status.localeCompare(b.verification_status);
        case 'trust_score':
          return (b.trust_score || 0) - (a.trust_score || 0);
        case 'created_at':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });

    setFilteredLandlords(filtered);
  }, [landlords, searchTerm, statusFilter, sortBy]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'bg-green-100 text-green-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusCount = (status: string) => {
    if (status === 'all') return landlords.length;
    return landlords.filter(l => l.verification_status === status).length;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Landlord Management</h2>
          <p className="text-muted-foreground">
            View and manage all landlords in the system
          </p>
        </div>
        <Button onClick={loadAllLandlords} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {[
          { status: 'all', label: 'Total Landlords', icon: User },
          { status: 'verified', label: 'Verified', icon: CheckCircle },
          { status: 'under_review', label: 'Under Review', icon: Clock },
          { status: 'rejected', label: 'Rejected', icon: XCircle },
          { status: 'suspended', label: 'Suspended', icon: AlertTriangle }
        ].map(({ status, label, icon: Icon }) => (
          <Card 
            key={status} 
            className={`cursor-pointer transition-colors ${statusFilter === status ? 'ring-2 ring-blue-500' : 'hover:bg-gray-50'}`}
            onClick={() => setStatusFilter(status)}
          >
            <CardContent className="p-4 text-center">
              <Icon className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
              <p className="text-2xl font-bold">{getStatusCount(status)}</p>
              <p className="text-sm text-muted-foreground">{label}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search" className="sr-only">Search</Label>
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name, email, phone, or ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">Date Created</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                  <SelectItem value="trust_score">Trust Score</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Landlords List */}
      <Card>
        <CardHeader>
          <CardTitle>
            Landlords ({filteredLandlords.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 mx-auto mb-2 text-muted-foreground animate-spin" />
              <p className="text-muted-foreground">Loading landlords...</p>
            </div>
          ) : filteredLandlords.length === 0 ? (
            <div className="text-center py-8">
              <User className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-muted-foreground">No landlords found matching your criteria</p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredLandlords.map((landlord) => (
                <Card 
                  key={landlord.id}
                  className="cursor-pointer hover:shadow-md transition-all duration-200"
                  onClick={() => setSelectedLandlord(landlord)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex-shrink-0">
                          <User className="h-10 w-10 text-muted-foreground" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {landlord.full_name}
                          </p>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {landlord.id}
                            </span>
                            {landlord.phone && (
                              <span className="flex items-center gap-1">
                                <Phone className="h-3 w-3" />
                                {landlord.phone}
                              </span>
                            )}
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(landlord.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        {landlord.trust_score && (
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="text-sm">{landlord.trust_score}</span>
                          </div>
                        )}
                        <Badge className={getStatusColor(landlord.verification_status)}>
                          {landlord.verification_status}
                        </Badge>
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Landlord Details Modal */}
      {selectedLandlord && (
        <Dialog open={!!selectedLandlord} onOpenChange={() => setSelectedLandlord(null)}>
          <LandlordDetails 
            landlord={selectedLandlord}
            onClose={() => setSelectedLandlord(null)}
            onStatusUpdate={loadAllLandlords}
          />
        </Dialog>
      )}
    </div>
  );
};
