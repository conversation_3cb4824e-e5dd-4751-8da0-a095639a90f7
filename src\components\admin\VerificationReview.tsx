import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  User, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  Star,
  Building,
  Phone,
  Mail,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import { useAdminVerification } from '@/hooks/useAdminVerification';
import { VerifiedProfile, VerificationDocument } from '@/types/verification';
import { regenerateDocumentUrl, getDocumentUrl } from '@/lib/documentUtils';

interface DocumentViewerProps {
  document: VerificationDocument;
  onReview: (documentId: string, approved: boolean, reason?: string) => void;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({ document, onReview }) => {
  const [reviewReason, setReviewReason] = useState('');
  const [open, setOpen] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [documentUrl, setDocumentUrl] = useState('');
  const [urlLoading, setUrlLoading] = useState(true);

  // Generate signed URL when component mounts or document changes
  useEffect(() => {
    const generateSignedUrl = async () => {
      setUrlLoading(true);
      try {
        const signedUrl = await getDocumentUrl(document.document_url);
        setDocumentUrl(signedUrl);
        setImageError(false);
      } catch (error) {
        console.error('Error generating signed URL:', error);
        setDocumentUrl(document.document_url); // Fallback to original
        setImageError(true);
      } finally {
        setUrlLoading(false);
      }
    };

    generateSignedUrl();
  }, [document.document_url]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      default: return 'text-yellow-600 bg-yellow-100';
    }
  };

  const handleReview = (approved: boolean) => {
    onReview(document.id, approved, reviewReason);
    setOpen(false);
    setReviewReason('');
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium capitalize">
                    {document.document_type.replace('_', ' ')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {document.document_name}
                  </p>
                </div>
              </div>
              <Badge className={getStatusColor(document.verification_status)}>
                {document.verification_status}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Review: {document.document_type.replace('_', ' ')}
          </DialogTitle>
          <DialogDescription>
            Review and approve or reject this verification document
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Document Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Document Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Document Type</Label>
                  <p className="capitalize">{document.document_type.replace('_', ' ')}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">File Name</Label>
                  <p>{document.document_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Upload Date</Label>
                  <p>{new Date(document.created_at).toLocaleDateString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">File Size</Label>
                  <p>{document.file_size ? `${(document.file_size / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}</p>
                </div>
              </div>
              
              {document.expiry_date && (
                <div>
                  <Label className="text-sm font-medium">Expiry Date</Label>
                  <p>{new Date(document.expiry_date).toLocaleDateString()}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Document Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Document Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-4 bg-gray-50">
                {urlLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-muted-foreground">Loading document...</p>
                  </div>
                ) : document.mime_type?.startsWith('image/') ? (
                  <div className="text-center">
                    {!imageError && documentUrl ? (
                      <>
                        <img
                          src={documentUrl}
                          alt="Document preview"
                          className="max-w-full h-auto rounded mx-auto"
                          onError={(e) => {
                            console.error('Failed to load image:', documentUrl);
                            setImageError(true);
                          }}
                        />
                        <div className="mt-2 text-xs text-muted-foreground">
                          Document ID: {document.id}
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-8">
                        <FileText className="h-12 w-12 mx-auto mb-2 text-red-500" />
                        <p className="text-red-600 font-medium">Failed to load image</p>
                        <p className="text-muted-foreground text-sm mb-2">Document ID: {document.id}</p>
                        <p className="text-muted-foreground text-xs mb-3 break-all">
                          Original URL: {document.document_url}
                        </p>
                        <p className="text-muted-foreground text-xs mb-3 break-all">
                          Current URL: {documentUrl}
                        </p>
                        <div className="space-y-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => window.open(documentUrl, '_blank')}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            Try Direct View
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={async () => {
                              setImageError(false);
                              setUrlLoading(true);
                              try {
                                const newUrl = await regenerateDocumentUrl(document.document_url);
                                setDocumentUrl(newUrl);
                              } catch (error) {
                                console.error('Error regenerating URL:', error);
                                setImageError(true);
                              } finally {
                                setUrlLoading(false);
                              }
                            }}
                          >
                            Retry Load
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-muted-foreground">PDF Document</p>
                    <Button 
                      variant="outline" 
                      className="mt-2"
                      onClick={() => window.open(documentUrl, '_blank')}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Document
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Review Section */}
          {document.verification_status === 'pending' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Review Decision</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="review-reason">Review Notes (Optional)</Label>
                  <Textarea
                    id="review-reason"
                    placeholder="Add notes about your review decision..."
                    value={reviewReason}
                    onChange={(e) => setReviewReason(e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="flex gap-3">
                  <Button 
                    onClick={() => handleReview(true)}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve Document
                  </Button>
                  <Button 
                    onClick={() => handleReview(false)}
                    variant="destructive"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject Document
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Previous Review */}
          {document.verification_status !== 'pending' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Review History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(document.verification_status)}>
                      {document.verification_status}
                    </Badge>
                    {document.reviewed_at && (
                      <span className="text-sm text-muted-foreground">
                        on {new Date(document.reviewed_at).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                  {document.rejection_reason && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded">
                      <p className="text-sm text-red-800">
                        <strong>Rejection Reason:</strong> {document.rejection_reason}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const VerificationReview: React.FC = () => {
  const { 
    getPendingVerifications, 
    getUserVerificationDocuments, 
    reviewLandlordVerification,
    reviewDocument,
    loading 
  } = useAdminVerification();
  
  const [pendingVerifications, setPendingVerifications] = useState<VerifiedProfile[]>([]);
  const [selectedLandlord, setSelectedLandlord] = useState<VerifiedProfile | null>(null);
  const [landlordDocuments, setLandlordDocuments] = useState<VerificationDocument[]>([]);
  const [reviewNotes, setReviewNotes] = useState('');
  const [trustScore, setTrustScore] = useState<number>(3.0);

  const loadPendingVerifications = useCallback(async () => {
    const verifications = await getPendingVerifications();
    setPendingVerifications(verifications);
  }, [getPendingVerifications]);

  useEffect(() => {
    loadPendingVerifications();
  }, [loadPendingVerifications]);

  const loadLandlordDocuments = async (landlordId: string) => {
    const documents = await getUserVerificationDocuments(landlordId);
    setLandlordDocuments(documents);
  };

  const handleLandlordSelect = async (landlord: VerifiedProfile) => {
    setSelectedLandlord(landlord);
    await loadLandlordDocuments(landlord.id);
    setReviewNotes(landlord.admin_notes || '');
    setTrustScore(landlord.trust_score || 3.0);
  };

  const handleLandlordReview = async (approved: boolean) => {
    if (!selectedLandlord) return;

    console.log('Starting landlord review:', { 
      landlordId: selectedLandlord.id, 
      approved, 
      currentStatus: selectedLandlord.verification_status 
    });

    const success = await reviewLandlordVerification(selectedLandlord.id, {
      verification_status: approved ? 'verified' : 'rejected',
      admin_notes: reviewNotes,
      trust_score: approved ? trustScore : 0,
      rejection_reason: approved ? undefined : reviewNotes,
    });

    if (success) {
      console.log('Review successful, refreshing data...');
      // Force immediate refresh of pending verifications
      await loadPendingVerifications();
      
      // Clear selection and reset form
      setSelectedLandlord(null);
      setReviewNotes('');
      setTrustScore(3.0);
      
      // Additional success feedback
      console.log('Review process completed');
    }
  };

  const handleDocumentReview = async (documentId: string, approved: boolean, reason?: string) => {
    const success = await reviewDocument(documentId, {
      verification_status: approved ? 'approved' : 'rejected',
      rejection_reason: reason,
    });

    if (success && selectedLandlord) {
      await loadLandlordDocuments(selectedLandlord.id);
    }
  };

  const getVerificationProgress = (documents: VerificationDocument[]) => {
    const requiredDocs = ['national_id'];
    const approvedDocs = documents.filter(doc => 
      requiredDocs.includes(doc.document_type) && doc.verification_status === 'approved'
    );
    return (approvedDocs.length / requiredDocs.length) * 100;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Verification Review</h2>
          <p className="text-muted-foreground">
            Review and approve landlord verification applications
          </p>
        </div>
        <Button onClick={loadPendingVerifications} disabled={loading}>
          <Clock className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Pending Verifications List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Pending Verifications ({pendingVerifications.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {pendingVerifications.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-600" />
                  <p className="text-muted-foreground">No pending verifications</p>
                </div>
              ) : (
                pendingVerifications.map((landlord) => (
                  <Card 
                    key={landlord.id}
                    className={`cursor-pointer transition-colors ${
                      selectedLandlord?.id === landlord.id ? 'ring-2 ring-blue-500' : 'hover:bg-gray-50'
                    }`}
                    onClick={() => handleLandlordSelect(landlord)}
                  >
                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">{landlord.full_name}</p>
                          <Badge variant="outline" className="text-yellow-600">
                            {landlord.verification_status}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {landlord.id}
                          </div>
                          {landlord.phone && (
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              {landlord.phone}
                            </div>
                          )}
                          {landlord.verification_submitted_at && (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              Submitted {new Date(landlord.verification_submitted_at).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </CardContent>
          </Card>
        </div>

        {/* Landlord Details and Review */}
        <div className="lg:col-span-2">
          {selectedLandlord ? (
            <div className="space-y-6">
              {/* Landlord Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    {selectedLandlord.full_name}
                  </CardTitle>
                  <CardDescription>
                    Landlord verification details and documents
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Full Name</Label>
                      <p>{selectedLandlord.full_name}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Phone</Label>
                      <p>{selectedLandlord.phone || 'Not provided'}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">National ID</Label>
                      <p>{selectedLandlord.national_id || 'Not provided'}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">KRA PIN</Label>
                      <p>{selectedLandlord.tax_pin || 'Not provided'}</p>
                    </div>
                    {selectedLandlord.business_name && (
                      <>
                        <div>
                          <Label className="text-sm font-medium">Business Name</Label>
                          <p>{selectedLandlord.business_name}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium">Years in Business</Label>
                          <p>{selectedLandlord.years_in_business || 'Not specified'}</p>
                        </div>
                      </>
                    )}
                  </div>

                  {selectedLandlord.business_address && (
                    <div>
                      <Label className="text-sm font-medium">Business Address</Label>
                      <p>{selectedLandlord.business_address}</p>
                    </div>
                  )}

                  {/* Verification Progress */}
                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between mb-2">
                      <Label className="text-sm font-medium">Document Verification Progress</Label>
                      <span className="text-sm text-muted-foreground">
                        {Math.round(getVerificationProgress(landlordDocuments))}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${getVerificationProgress(landlordDocuments)}%` }}
                      ></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Documents */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Verification Documents ({landlordDocuments.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {landlordDocuments.length === 0 ? (
                    <div className="text-center py-8">
                      <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-yellow-600" />
                      <p className="text-muted-foreground">No documents uploaded</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {landlordDocuments.map((document) => (
                        <DocumentViewer
                          key={document.id}
                          document={document}
                          onReview={handleDocumentReview}
                        />
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Review Decision */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    Final Review Decision
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="trust-score">Trust Score (1.0 - 5.0)</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="trust-score"
                        type="number"
                        min="1"
                        max="5"
                        step="0.1"
                        value={trustScore}
                        onChange={(e) => setTrustScore(parseFloat(e.target.value))}
                        className="w-24"
                      />
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-4 w-4 ${
                              star <= trustScore ? 'text-yellow-400 fill-current' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="review-notes">Admin Notes</Label>
                    <Textarea
                      id="review-notes"
                      placeholder="Add notes about this verification review..."
                      value={reviewNotes}
                      onChange={(e) => setReviewNotes(e.target.value)}
                      rows={4}
                    />
                  </div>

                  <div className="flex gap-3 pt-4">
                    <Button 
                      onClick={() => handleLandlordReview(true)}
                      className="bg-green-600 hover:bg-green-700"
                      disabled={loading}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve Verification
                    </Button>
                    <Button 
                      onClick={() => handleLandlordReview(false)}
                      variant="destructive"
                      disabled={loading}
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject Application
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Select a Verification</h3>
                <p className="text-muted-foreground">
                  Choose a pending verification from the list to review documents and make a decision.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
