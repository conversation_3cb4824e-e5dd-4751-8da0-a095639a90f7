import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { performanceMonitor } from '@/utils/performance';
import { globalCache, useCache } from '@/utils/cache';
import { RefreshCw, Trash2, AlertTriangle, Database, Clock, BarChart3 } from 'lucide-react';

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
}

export const PerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const cache = useCache();

  const refreshMetrics = () => {
    setMetrics(performanceMonitor.getMetrics());
  };

  const clearMetrics = () => {
    performanceMonitor.clearMetrics();
    setMetrics([]);
  };

  useEffect(() => {
    refreshMetrics();
    const interval = setInterval(refreshMetrics, 2000);
    return () => clearInterval(interval);
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const slowOperations = metrics.filter(m => m.duration > 1000);
  const averageDuration = metrics.length > 0 
    ? metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length 
    : 0;

  const cacheStats = cache.getStats();
  const recentMetrics = metrics.slice(-20).reverse();

  // Group metrics by operation type
  const metricsByOperation = metrics.reduce((acc, metric) => {
    if (!acc[metric.operation]) {
      acc[metric.operation] = [];
    }
    acc[metric.operation].push(metric);
    return acc;
  }, {} as Record<string, PerformanceMetric[]>);

  const operationStats = Object.entries(metricsByOperation).map(([operation, ops]) => ({
    operation,
    count: ops.length,
    avgDuration: ops.reduce((sum, op) => sum + op.duration, 0) / ops.length,
    maxDuration: Math.max(...ops.map(op => op.duration)),
    minDuration: Math.min(...ops.map(op => op.duration)),
  }));

  return (
    <>
      {/* Toggle button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 left-4 z-50"
      >
        {slowOperations.length > 0 && (
          <AlertTriangle className="h-4 w-4 mr-2 text-orange-500" />
        )}
        <BarChart3 className="h-4 w-4 mr-2" />
        Performance
      </Button>

      {/* Dashboard panel */}
      {isVisible && (
        <Card className="fixed bottom-16 left-4 w-[600px] max-h-[500px] overflow-auto z-50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center justify-between">
              Performance Dashboard
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={refreshMetrics}>
                  <RefreshCw className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" onClick={clearMetrics}>
                  <Trash2 className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" onClick={() => setIsVisible(false)}>
                  ×
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="metrics">Metrics</TabsTrigger>
                <TabsTrigger value="cache">Cache</TabsTrigger>
                <TabsTrigger value="operations">Operations</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total Operations:</span>
                      <Badge variant="outline">{metrics.length}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Duration:</span>
                      <Badge variant={averageDuration > 500 ? "destructive" : "default"}>
                        {averageDuration.toFixed(1)}ms
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Slow Operations:</span>
                      <Badge variant={slowOperations.length > 0 ? "destructive" : "default"}>
                        {slowOperations.length}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Cache Size:</span>
                      <Badge variant="outline">{cacheStats.size}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Memory Usage:</span>
                      <Badge variant="outline">
                        {(performance.memory?.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB
                      </Badge>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="metrics" className="space-y-2">
                <div className="max-h-60 overflow-y-auto space-y-1">
                  {recentMetrics.map((metric, index) => (
                    <div key={index} className="flex items-center justify-between text-xs p-2 rounded bg-muted/50">
                      <span className="truncate flex-1">{metric.operation}</span>
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant={metric.duration > 1000 ? "destructive" : 
                                  metric.duration > 500 ? "secondary" : "default"}
                          className="text-xs"
                        >
                          {metric.duration.toFixed(0)}ms
                        </Badge>
                        <span className="text-muted-foreground">
                          {new Date(metric.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="cache" className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs font-medium">Cache Keys ({cacheStats.size})</span>
                  <Button variant="outline" size="sm" onClick={() => cache.clear()}>
                    Clear All
                  </Button>
                </div>
                <div className="max-h-60 overflow-y-auto space-y-1">
                  {cacheStats.keys.map((key, index) => (
                    <div key={index} className="flex items-center justify-between text-xs p-2 rounded bg-muted/50">
                      <span className="truncate flex-1">{key}</span>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => cache.delete(key)}
                        className="h-6 w-6 p-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="operations" className="space-y-2">
                <div className="max-h-60 overflow-y-auto space-y-2">
                  {operationStats.map((stat, index) => (
                    <div key={index} className="p-2 rounded bg-muted/50">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs font-medium truncate">{stat.operation}</span>
                        <Badge variant="outline" className="text-xs">{stat.count}x</Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                        <div>Avg: {stat.avgDuration.toFixed(0)}ms</div>
                        <div>Min: {stat.minDuration.toFixed(0)}ms</div>
                        <div>Max: {stat.maxDuration.toFixed(0)}ms</div>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </>
  );
};
