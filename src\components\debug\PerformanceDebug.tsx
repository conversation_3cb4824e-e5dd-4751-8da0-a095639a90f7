import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { performanceMonitor } from '@/utils/performance';
import { RefreshCw, Trash2, AlertTriangle } from 'lucide-react';

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
}

export const PerformanceDebug: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  const refreshMetrics = () => {
    setMetrics(performanceMonitor.getMetrics());
  };

  const clearMetrics = () => {
    performanceMonitor.clearMetrics();
    setMetrics([]);
  };

  useEffect(() => {
    refreshMetrics();
    const interval = setInterval(refreshMetrics, 2000);
    return () => clearInterval(interval);
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const slowOperations = metrics.filter(m => m.duration > 1000);
  const averageDuration = metrics.length > 0 
    ? metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length 
    : 0;

  return (
    <>
      {/* Toggle button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50"
      >
        {slowOperations.length > 0 && (
          <AlertTriangle className="h-4 w-4 mr-2 text-orange-500" />
        )}
        Perf Debug
      </Button>

      {/* Debug panel */}
      {isVisible && (
        <Card className="fixed bottom-16 right-4 w-96 max-h-96 overflow-auto z-50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center justify-between">
              Performance Metrics
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={refreshMetrics}>
                  <RefreshCw className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" onClick={clearMetrics}>
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="text-xs text-muted-foreground">
                Total Operations: {metrics.length} | 
                Avg Duration: {averageDuration.toFixed(1)}ms |
                Slow Ops: {slowOperations.length}
              </div>
              
              {metrics.slice(-10).reverse().map((metric, index) => (
                <div key={index} className="flex items-center justify-between text-xs">
                  <span className="truncate flex-1">{metric.operation}</span>
                  <Badge 
                    variant={metric.duration > 1000 ? "destructive" : 
                            metric.duration > 500 ? "secondary" : "default"}
                    className="ml-2"
                  >
                    {metric.duration.toFixed(0)}ms
                  </Badge>
                </div>
              ))}
              
              {metrics.length === 0 && (
                <div className="text-xs text-muted-foreground text-center py-4">
                  No metrics recorded yet
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
};
