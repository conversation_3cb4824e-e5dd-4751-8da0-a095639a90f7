import { cn } from "@/lib/utils";

interface LoadingSkeletonProps {
  className?: string;
  lines?: number;
  showAvatar?: boolean;
}

export const LoadingSkeleton = ({ 
  className, 
  lines = 3, 
  showAvatar = false 
}: LoadingSkeletonProps) => {
  return (
    <div className={cn("animate-pulse", className)}>
      {showAvatar && (
        <div className="flex items-center space-x-4 mb-4">
          <div className="rounded-full bg-muted h-10 w-10"></div>
          <div className="space-y-2">
            <div className="h-4 bg-muted rounded w-32"></div>
            <div className="h-3 bg-muted rounded w-24"></div>
          </div>
        </div>
      )}
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, i) => (
          <div
            key={i}
            className={cn(
              "h-4 bg-muted rounded",
              i === lines - 1 ? "w-3/4" : "w-full"
            )}
          />
        ))}
      </div>
    </div>
  );
};

interface ProfileLoadingSkeletonProps {
  className?: string;
}

export const ProfileLoadingSkeleton = ({ className }: ProfileLoadingSkeletonProps) => {
  return (
    <div className={cn("space-y-6", className)}>
      {/* Header skeleton */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <div className="h-8 w-20 bg-muted animate-pulse rounded"></div>
        </div>
        <div className="h-6 w-48 bg-muted animate-pulse rounded"></div>
        <div className="h-4 w-64 bg-muted animate-pulse rounded"></div>
      </div>
      
      {/* Form fields skeleton */}
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="h-4 w-24 bg-muted animate-pulse rounded"></div>
          <div className="h-10 w-full bg-muted animate-pulse rounded"></div>
        </div>
        <div className="space-y-4">
          <div className="h-4 w-24 bg-muted animate-pulse rounded"></div>
          <div className="h-10 w-full bg-muted animate-pulse rounded"></div>
        </div>
        <div className="h-10 w-32 bg-muted animate-pulse rounded"></div>
      </div>
    </div>
  );
};
