import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Shield, User, Building, FileText, CheckCircle } from 'lucide-react';
import { useVerification } from '@/hooks/useVerification';
import { DocumentUpload } from './DocumentUpload';
import { LandlordVerificationForm as FormData } from '@/types/verification';

interface LandlordVerificationFormProps {
  onVerificationSubmitted?: () => void;
}

export const LandlordVerificationForm: React.FC<LandlordVerificationFormProps> = ({
  onVerificationSubmitted
}) => {
  const { submitLandlordVerification, loading, verificationDocuments } = useVerification();
  const [currentTab, setCurrentTab] = useState('personal');
  const [formData, setFormData] = useState<Omit<FormData, 'documents'>>({
    national_id: '',
    business_registration_number: '',
    tax_pin: '',
    business_name: '',
    business_address: '',
    years_in_business: undefined,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validatePersonalInfo = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.national_id.trim()) {
      newErrors.national_id = 'National ID is required';
    } else if (!/^\d{8}$/.test(formData.national_id.trim())) {
      newErrors.national_id = 'National ID must be 8 digits';
    }

    if (formData.tax_pin && !/^[A-Z]\d{9}[A-Z]$/.test(formData.tax_pin.trim())) {
      newErrors.tax_pin = 'Invalid KRA PIN format (e.g., A123456789B)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateBusinessInfo = () => {
    const newErrors: Record<string, string> = {};

    if (formData.business_name && !formData.business_name.trim()) {
      newErrors.business_name = 'Business name cannot be empty if provided';
    }

    if (formData.years_in_business && (formData.years_in_business < 0 || formData.years_in_business > 50)) {
      newErrors.years_in_business = 'Years in business must be between 0 and 50';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateDocuments = () => {
    const requiredDocs = ['national_id'] as const;
    const uploadedDocTypes = verificationDocuments.map(doc => doc.document_type);
    const missingDocs = requiredDocs.filter(docType => !uploadedDocTypes.includes(docType));

    if (missingDocs.length > 0) {
      setErrors({
        documents: `Missing required documents: ${missingDocs.join(', ')}`
      });
      return false;
    }

    setErrors({});
    return true;
  };

  const handleSubmit = async () => {
    // Validate all sections
    const personalValid = validatePersonalInfo();
    const businessValid = validateBusinessInfo();
    const documentsValid = validateDocuments();

    if (!personalValid || !businessValid || !documentsValid) {
      return;
    }

    const success = await submitLandlordVerification({
      ...formData,
      documents: [], // Documents are already uploaded
    });

    if (success) {
      onVerificationSubmitted?.();
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const isTabComplete = (tab: string) => {
    switch (tab) {
      case 'personal':
        return formData.national_id.trim() !== '';
      case 'business':
        return true; // Business info is optional
      case 'documents': {
        const requiredDocs = ['national_id'] as const;
        const uploadedDocTypes = verificationDocuments.map(doc => doc.document_type);
        return requiredDocs.every(docType => uploadedDocTypes.includes(docType));
      }
      default:
        return false;
    }
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-6 w-6 text-blue-600" />
          Landlord Verification Application
        </CardTitle>
        <CardDescription>
          Complete your verification to build trust with potential tenants and access premium features.
          This process helps protect both landlords and tenants from fraud.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={currentTab} onValueChange={setCurrentTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="personal" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Personal Info
              {isTabComplete('personal') && <CheckCircle className="h-4 w-4 text-green-600" />}
            </TabsTrigger>
            <TabsTrigger value="business" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              Business Info
              {isTabComplete('business') && <CheckCircle className="h-4 w-4 text-green-600" />}
            </TabsTrigger>
            <TabsTrigger value="documents" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Documents
              {isTabComplete('documents') && <CheckCircle className="h-4 w-4 text-green-600" />}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="personal" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="national_id">National ID Number *</Label>
                <Input
                  id="national_id"
                  placeholder="12345678"
                  value={formData.national_id}
                  onChange={(e) => handleInputChange('national_id', e.target.value)}
                  className={errors.national_id ? 'border-red-500' : ''}
                />
                {errors.national_id && (
                  <p className="text-sm text-red-600">{errors.national_id}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="tax_pin">KRA PIN (Optional)</Label>
                <Input
                  id="tax_pin"
                  placeholder="A123456789B"
                  value={formData.tax_pin}
                  onChange={(e) => handleInputChange('tax_pin', e.target.value.toUpperCase())}
                  className={errors.tax_pin ? 'border-red-500' : ''}
                />
                {errors.tax_pin && (
                  <p className="text-sm text-red-600">{errors.tax_pin}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  Providing your KRA PIN helps verify your tax compliance status
                </p>
              </div>
            </div>

            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                Your personal information is encrypted and securely stored. It will only be used for verification purposes
                and will not be shared with tenants or third parties.
              </AlertDescription>
            </Alert>

            <div className="flex justify-end">
              <Button 
                onClick={() => {
                  if (validatePersonalInfo()) {
                    setCurrentTab('business');
                  }
                }}
              >
                Continue to Business Info
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="business" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="business_name">Business Name (Optional)</Label>
                <Input
                  id="business_name"
                  placeholder="ABC Property Management"
                  value={formData.business_name}
                  onChange={(e) => handleInputChange('business_name', e.target.value)}
                  className={errors.business_name ? 'border-red-500' : ''}
                />
                {errors.business_name && (
                  <p className="text-sm text-red-600">{errors.business_name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="business_registration_number">Business Registration Number (Optional)</Label>
                <Input
                  id="business_registration_number"
                  placeholder="BRS/2023/123456"
                  value={formData.business_registration_number}
                  onChange={(e) => handleInputChange('business_registration_number', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="years_in_business">Years in Business (Optional)</Label>
                <Input
                  id="years_in_business"
                  type="number"
                  min="0"
                  max="50"
                  placeholder="5"
                  value={formData.years_in_business || ''}
                  onChange={(e) => handleInputChange('years_in_business', parseInt(e.target.value) || 0)}
                  className={errors.years_in_business ? 'border-red-500' : ''}
                />
                {errors.years_in_business && (
                  <p className="text-sm text-red-600">{errors.years_in_business}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="business_address">Business Address (Optional)</Label>
              <Textarea
                id="business_address"
                placeholder="123 Business Street, Nairobi, Kenya"
                value={formData.business_address}
                onChange={(e) => handleInputChange('business_address', e.target.value)}
                rows={3}
              />
            </div>

            <Alert>
              <Building className="h-4 w-4" />
              <AlertDescription>
                Business information is optional but helps establish credibility. Registered businesses
                may receive higher trust scores and priority in search results.
              </AlertDescription>
            </Alert>

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setCurrentTab('personal')}>
                Back to Personal Info
              </Button>
              <Button 
                onClick={() => {
                  if (validateBusinessInfo()) {
                    setCurrentTab('documents');
                  }
                }}
              >
                Continue to Documents
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <DocumentUpload />

            {errors.documents && (
              <Alert variant="destructive">
                <AlertDescription>{errors.documents}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setCurrentTab('business')}>
                Back to Business Info
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={loading || !isTabComplete('documents')}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? 'Submitting...' : 'Submit Verification Application'}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
