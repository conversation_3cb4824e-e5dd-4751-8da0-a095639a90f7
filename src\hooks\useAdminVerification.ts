import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from '@/hooks/use-toast';
import {
  VerificationDocument,
  VerifiedProfile,
  FraudReport,
  AdminVerificationReview,
  DocumentReview,
  VerificationStats
} from '@/types/verification';

export const useAdminVerification = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<VerificationStats | null>(null);

  // Check if user is admin
  const isAdmin = useCallback(async () => {
    if (!user) return false;
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('user_role')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      return data.user_role === 'admin' || data.user_role === 'super_admin';
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }, [user]);

  // Get verification statistics
  const getVerificationStats = useCallback(async (): Promise<VerificationStats | null> => {
    try {
      // Get landlord counts by verification status
      const { data: landlordStats, error: landlordError } = await supabase
        .from('profiles')
        .select('verification_status')
        .eq('user_role', 'landlord');

      if (landlordError) throw landlordError;

      // Get fraud reports count
      const { count: fraudReportsCount, error: fraudError } = await supabase
        .from('fraud_reports')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'open');

      if (fraudError) throw fraudError;

      // Get properties pending approval
      const { count: pendingPropertiesCount, error: propertiesError } = await supabase
        .from('properties')
        .select('*', { count: 'exact', head: true })
        .eq('approval_status', 'pending');

      if (propertiesError) throw propertiesError;

      // Calculate stats
      const totalLandlords = landlordStats.length;
      const pendingVerifications = landlordStats.filter(l => l.verification_status === 'pending' || l.verification_status === 'under_review').length;
      const verifiedLandlords = landlordStats.filter(l => l.verification_status === 'verified').length;
      const rejectedApplications = landlordStats.filter(l => l.verification_status === 'rejected').length;
      const flaggedAccounts = landlordStats.filter(l => l.verification_status === 'suspended').length;

      const statsData: VerificationStats = {
        total_landlords: totalLandlords,
        pending_verifications: pendingVerifications,
        verified_landlords: verifiedLandlords,
        rejected_applications: rejectedApplications,
        flagged_accounts: flaggedAccounts,
        open_fraud_reports: fraudReportsCount || 0,
        properties_pending_approval: pendingPropertiesCount || 0,
        average_verification_time_days: 3.5, // This would be calculated from actual data
      };

      setStats(statsData);
      return statsData;
    } catch (error) {
      console.error('Error getting verification stats:', error);
      return null;
    }
  }, []);

  // Get pending verifications
  const getPendingVerifications = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .in('verification_status', ['pending', 'under_review'])
        .eq('user_role', 'landlord')
        .order('verification_submitted_at', { ascending: true });

      if (error) throw error;
      return data as VerifiedProfile[];
    } catch (error) {
      console.error('Error getting pending verifications:', error);
      return [];
    }
  };

  // Get all landlords (for landlord management)
  const getAllLandlords = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_role', 'landlord')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as VerifiedProfile[];
    } catch (error) {
      console.error('Error getting all landlords:', error);
      return [];
    }
  };

  // Get verification documents for a user
  const getUserVerificationDocuments = useCallback(async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('verification_documents')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Return documents without generating signed URLs upfront
      // Signed URLs will be generated when documents are actually viewed
      return data as VerificationDocument[];
    } catch (error) {
      console.error('Error getting user documents:', error);
      return [];
    }
  }, []);

  // Review landlord verification
  const reviewLandlordVerification = async (userId: string, review: AdminVerificationReview) => {
    if (!user) return false;

    setLoading(true);
    try {
      console.log('Reviewing landlord verification for user:', userId, 'with status:', review.verification_status);
      
      const { error } = await supabase
        .from('profiles')
        .update({
          verification_status: review.verification_status,
          verification_completed_at: new Date().toISOString(),
          verified_by: user.id,
          admin_notes: review.admin_notes,
          trust_score: review.trust_score || 0,
        })
        .eq('id', userId);

      if (error) {
        console.error('Error updating profile:', error);
        throw error;
      }

      // If rejected, update with rejection reason
      if (review.verification_status === 'rejected' && review.rejection_reason) {
        await supabase
          .from('profiles')
          .update({
            admin_notes: `${review.admin_notes || ''}\n\nRejection Reason: ${review.rejection_reason}`.trim()
          })
          .eq('id', userId);
      }

      console.log('Verification review completed successfully');

      toast({
        title: "Verification reviewed",
        description: `Landlord verification has been ${review.verification_status}`,
      });

      return true;
    } catch (error) {
      console.error('Error reviewing verification:', error);
      toast({
        title: "Review failed",
        description: error instanceof Error ? error.message : "Failed to review verification",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Review document
  const reviewDocument = async (documentId: string, review: DocumentReview) => {
    if (!user) return false;

    setLoading(true);
    try {
      const { error } = await supabase
        .from('verification_documents')
        .update({
          verification_status: review.verification_status,
          reviewed_by: user.id,
          reviewed_at: new Date().toISOString(),
          rejection_reason: review.rejection_reason,
        })
        .eq('id', documentId);

      if (error) throw error;

      toast({
        title: "Document reviewed",
        description: `Document has been ${review.verification_status}`,
      });

      return true;
    } catch (error) {
      console.error('Error reviewing document:', error);
      toast({
        title: "Review failed",
        description: error instanceof Error ? error.message : "Failed to review document",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Get fraud reports
  const getFraudReports = async (status?: string) => {
    try {
      let query = supabase
        .from('fraud_reports')
        .select(`
          *,
          profiles!fraud_reports_reported_user_id_fkey (full_name, phone),
          properties (title, location)
        `)
        .order('created_at', { ascending: false });

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as FraudReport[];
    } catch (error) {
      console.error('Error getting fraud reports:', error);
      return [];
    }
  };

  // Update fraud report status
  const updateFraudReportStatus = async (
    reportId: string, 
    status: string, 
    resolutionNotes?: string
  ) => {
    if (!user) return false;

    setLoading(true);
    try {
      const updateData: {
        status: string;
        assigned_to: string;
        resolved_at?: string;
        resolution_notes?: string;
      } = {
        status,
        assigned_to: user.id,
      };

      if (status === 'resolved' || status === 'dismissed') {
        updateData.resolved_at = new Date().toISOString();
        updateData.resolution_notes = resolutionNotes;
      }

      const { error } = await supabase
        .from('fraud_reports')
        .update(updateData)
        .eq('id', reportId);

      if (error) throw error;

      toast({
        title: "Report updated",
        description: `Fraud report status updated to ${status}`,
      });

      return true;
    } catch (error) {
      console.error('Error updating fraud report:', error);
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "Failed to update fraud report",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Flag/unflag user account
  const flagUserAccount = async (userId: string, flagged: boolean, reason?: string) => {
    if (!user) return false;

    setLoading(true);
    try {
      const updateData: {
        is_flagged: boolean;
        flagged_reason?: string | null;
        flagged_at?: string | null;
        flagged_by?: string | null;
      } = {
        is_flagged: flagged,
      };

      if (flagged) {
        updateData.flagged_reason = reason;
        updateData.flagged_at = new Date().toISOString();
        updateData.flagged_by = user.id;
      } else {
        updateData.flagged_reason = null;
        updateData.flagged_at = null;
        updateData.flagged_by = null;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);

      if (error) throw error;

      toast({
        title: flagged ? "Account flagged" : "Account unflagged",
        description: flagged ? "User account has been flagged for review" : "Flag removed from user account",
      });

      return true;
    } catch (error) {
      console.error('Error flagging account:', error);
      toast({
        title: "Action failed",
        description: error instanceof Error ? error.message : "Failed to update account flag",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Approve/reject property
  const reviewProperty = async (
    propertyId: string, 
    approved: boolean, 
    rejectionReason?: string
  ) => {
    if (!user) return false;

    setLoading(true);
    try {
      const { error } = await supabase
        .from('properties')
        .update({
          approval_status: approved ? 'approved' : 'rejected',
          approved_by: user.id,
          approved_at: new Date().toISOString(),
          rejection_reason: rejectionReason,
        })
        .eq('id', propertyId);

      if (error) throw error;

      toast({
        title: approved ? "Property approved" : "Property rejected",
        description: `Property has been ${approved ? 'approved' : 'rejected'}`,
      });

      return true;
    } catch (error) {
      console.error('Error reviewing property:', error);
      toast({
        title: "Review failed",
        description: error instanceof Error ? error.message : "Failed to review property",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const checkAdminAndLoadStats = async () => {
      const adminStatus = await isAdmin();
      if (adminStatus) {
        await getVerificationStats();
      }
    };

    if (user) {
      checkAdminAndLoadStats();
    }
  }, [user, isAdmin, getVerificationStats]);

  return {
    loading,
    stats,
    isAdmin,
    getVerificationStats,
    getPendingVerifications,
    getAllLandlords,
    getUserVerificationDocuments,
    reviewLandlordVerification,
    reviewDocument,
    getFraudReports,
    updateFraudReportStatus,
    flagUserAccount,
    reviewProperty,
  };
};
