import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from './use-toast';
import { PropertyWithImages } from '@/types/property';

export const useFavorites = () => {
  const [favorites, setFavorites] = useState<string[]>([]);
  const [favoriteProperties, setFavoriteProperties] = useState<PropertyWithImages[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const fetchFavorites = useCallback(async () => {
    if (!user) {
      setFavorites([]);
      setFavoriteProperties([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('favorites')
        .select('property_id')
        .eq('user_id', user.id);

      if (error) throw error;

      const favoriteIds = data?.map(fav => fav.property_id) || [];
      setFavorites(favoriteIds);
    } catch (error) {
      console.error('Error fetching favorites:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  const fetchFavoriteProperties = useCallback(async () => {
    if (!user || isFetching) {
      if (!user) {
        setFavoriteProperties([]);
        setLoading(false);
      }
      return;
    }

    try {
      setIsFetching(true);
      setLoading(true);
      
      // First, get the favorite property IDs
      const { data: favoriteData, error: favoriteError } = await supabase
        .from('favorites')
        .select('property_id')
        .eq('user_id', user.id);

      if (favoriteError) throw favoriteError;

      const propertyIds = favoriteData?.map(fav => fav.property_id) || [];
      
      if (propertyIds.length === 0) {
        setFavoriteProperties([]);
        return;
      }

      // Then get the properties with their images
      const { data: propertiesData, error: propertiesError } = await supabase
        .from('properties')
        .select(`
          id,
          user_id,
          title,
          description,
          rent,
          location,
          county,
          room_type,
          bedrooms,
          bathrooms,
          area,
          amenities,
          available,
          featured,
          latitude,
          longitude,
          formatted_address,
          neighborhood,
          city,
          is_multi_unit,
          building_name,
          total_units,
          property_type,
          created_at,
          updated_at,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          )
        `)
        .in('id', propertyIds);

      if (propertiesError) throw propertiesError;

      // Get the owner profiles separately
      const ownerIds = propertiesData?.map(prop => prop.user_id).filter(Boolean) || [];
      
      let profilesData: Array<{
        id: string;
        full_name: string | null;
        phone: string | null;
        created_at: string;
        updated_at: string;
      }> = [];
      if (ownerIds.length > 0) {
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select(`
            id,
            full_name,
            phone,
            created_at,
            updated_at
          `)
          .in('id', ownerIds);

        if (profilesError) {
          console.warn('Error fetching profiles:', profilesError);
        } else {
          profilesData = profiles || [];
        }
      }

      // Combine properties with their owner profiles
      const propertiesWithProfiles = propertiesData?.map(property => ({
        ...property,
        profiles: profilesData.find(profile => profile.id === property.user_id) || null
      })) || [];

      setFavoriteProperties(propertiesWithProfiles);
    } catch (error) {
      console.error('Error fetching favorite properties:', error);
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  }, [user, isFetching]);

  const toggleFavorite = async (propertyId: string) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please log in to save favorites",
        variant: "destructive",
      });
      return;
    }

    const isFavorite = favorites.includes(propertyId);

    try {
      if (isFavorite) {
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('user_id', user.id)
          .eq('property_id', propertyId);

        if (error) throw error;

        setFavorites(prev => prev.filter(id => id !== propertyId));
        setFavoriteProperties(prev => prev.filter(prop => prop.id !== propertyId));
        
        toast({
          title: "Removed from favorites",
          description: "Property removed from your favorites",
        });
      } else {
        const { error } = await supabase
          .from('favorites')
          .insert({ user_id: user.id, property_id: propertyId });

        if (error) throw error;

        setFavorites(prev => [...prev, propertyId]);
        
        toast({
          title: "Added to favorites",
          description: "Property added to your favorites",
        });
      }
    } catch (error) {
      toast({
        title: "Error updating favorites",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
    }
  };

  const isFavorite = (propertyId: string) => favorites.includes(propertyId);

  const clearAllFavorites = async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('favorites')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;

      setFavorites([]);
      setFavoriteProperties([]);
      
      toast({
        title: "Favorites cleared",
        description: "All favorites have been removed",
      });
    } catch (error) {
      toast({
        title: "Error clearing favorites",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchFavorites();
  }, [fetchFavorites]);

  return {
    favorites,
    favoriteProperties,
    loading,
    toggleFavorite,
    isFavorite,
    fetchFavorites,
    fetchFavoriteProperties,
    clearAllFavorites,
  };
};
