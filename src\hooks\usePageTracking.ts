import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Hook to track page navigation and prevent unnecessary operations
 * when navigating to the same page
 */
export const usePageTracking = () => {
  const location = useLocation();
  const previousPath = useRef<string>('');
  const isNavigatingToSamePage = useRef<boolean>(false);

  useEffect(() => {
    const currentPath = location.pathname;
    isNavigatingToSamePage.current = previousPath.current === currentPath;
    previousPath.current = currentPath;
  }, [location.pathname]);

  return {
    currentPath: location.pathname,
    previousPath: previousPath.current,
    isNavigatingToSamePage: isNavigatingToSamePage.current,
  };
};
