import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from './use-toast';
import { Profile } from '@/types/property';
import { performanceMonitor } from '@/utils/performance';

interface ProfileFormData {
  full_name?: string;
  phone?: string;
}

export const useProfile = () => {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isCreatingProfile, setIsCreatingProfile] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  const { user } = useAuth();
  const { toast } = useToast();

  // Cache duration in milliseconds (5 minutes)
  const CACHE_DURATION = 5 * 60 * 1000;

  const createProfile = useCallback(async (profileData?: ProfileFormData) => {
    if (!user || isCreatingProfile) return null;

    setIsCreatingProfile(true);
    try {
      // First check if profile already exists to avoid duplicates
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .maybeSingle(); // Use maybeSingle instead of single to avoid throwing on no rows

      // If there's an error other than "no rows", handle it
      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('Error checking existing profile:', fetchError);
        throw fetchError;
      }

      if (existingProfile) {
        console.log('Profile already exists, refetching data...');
        // Refetch the full profile data
        const { data: fullProfile, error: refetchError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (refetchError) {
          console.error('Error refetching profile:', refetchError);
          throw refetchError;
        }
        
        if (fullProfile) {
          setProfile(fullProfile);
        }
        return existingProfile;
      }

      // Create new profile
      const profileToInsert = {
        id: user.id,
        full_name: profileData?.full_name || user.user_metadata?.full_name || '',
        phone: profileData?.phone || user.user_metadata?.phone || '',
      };

      console.log('Creating new profile with data:', profileToInsert);

      const { data, error } = await supabase
        .from('profiles')
        .insert([profileToInsert])
        .select('*')
        .single();

      if (error) {
        // Handle unique constraint violation (profile already exists)
        if (error.code === '23505') {
          console.log('Profile already exists (unique constraint), refetching...');
          const { data: fullProfile, error: refetchError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
          
          if (refetchError) {
            console.error('Error refetching after conflict:', refetchError);
            throw refetchError;
          }
          
          if (fullProfile) {
            setProfile(fullProfile);
          }
          return fullProfile;
        }
        
        console.error('Profile creation error:', error);
        throw error;
      }

      setProfile(data);
      toast({
        title: "Profile created",
        description: "Your profile has been created successfully",
      });

      return data;
    } catch (error) {
      console.error('Error creating profile:', error);
      toast({
        title: "Error creating profile",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return null;
    } finally {
      setIsCreatingProfile(false);
    }
  }, [user, toast, isCreatingProfile]);

  const fetchProfile = useCallback(async (forceRefresh = false) => {
    if (!user) {
      setProfile(null);
      setLoading(false);
      return;
    }

    // Check cache validity
    const now = Date.now();
    const isCacheValid = profile && (now - lastFetchTime) < CACHE_DURATION;

    if (!forceRefresh && isCacheValid) {
      console.log('Using cached profile data');
      setLoading(false);
      return;
    }

    try {
      performanceMonitor.startTimer('profile-fetch');

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle(); // Use maybeSingle to avoid throwing on no rows

      performanceMonitor.endTimer('profile-fetch');

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching profile:', error);
        throw error;
      }

      if (!data) {
        // Profile doesn't exist, create one
        console.log('Profile not found, creating new profile...');
        await createProfile();
        return;
      }

      console.log('Profile fetched successfully:', data);
      setProfile(data);
      setLastFetchTime(now);
    } catch (error) {
      console.error('Error fetching profile:', error);

      // Handle specific errors
      if (error instanceof Error) {
        if (error.message.includes('406')) {
          toast({
            title: "Database Access Error",
            description: "There's an issue with the database configuration. Please check your RLS policies.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Error fetching profile",
            description: error.message,
            variant: "destructive",
          });
        }
      }
    } finally {
      setLoading(false);
    }
  }, [user, toast, createProfile, profile, lastFetchTime, CACHE_DURATION]);

  const updateProfile = async (updates: ProfileFormData) => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)
        .select('*')
        .single();

      if (error) throw error;

      setProfile(data);
      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully",
      });

      return data;
    } catch (error) {
      toast({
        title: "Error updating profile",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return null;
    }
  };

  const deleteProfile = async () => {
    if (!user) return false;

    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', user.id);

      if (error) throw error;

      setProfile(null);
      toast({
        title: "Profile deleted",
        description: "Your profile has been deleted",
      });

      return true;
    } catch (error) {
      toast({
        title: "Error deleting profile",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return false;
    }
  };

  const refreshProfile = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    await fetchProfile(true); // Force refresh
  }, [user, fetchProfile]);

  useEffect(() => {
    fetchProfile();
    
    // Set up real-time subscription for profile changes
    if (user) {
      const subscription = supabase
        .channel(`profile_changes_${user.id}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'profiles',
            filter: `id=eq.${user.id}`,
          },
          (payload) => {
            console.log('Profile updated via real-time:', payload);
            const newProfile = payload.new as Profile;
            setProfile(newProfile);
            
            // Show toast notification for verification status changes
            if (newProfile.verification_status !== profile?.verification_status) {
              if (newProfile.verification_status === 'verified') {
                toast({
                  title: "Verification Complete!",
                  description: "Your landlord account has been successfully verified.",
                });
              } else if (newProfile.verification_status === 'rejected') {
                toast({
                  title: "Verification Rejected",
                  description: "Your verification application was rejected. Please review and resubmit.",
                  variant: "destructive",
                });
              }
            }
            
            // Trigger navigation if user becomes super_admin
            if (newProfile.user_role === 'super_admin' && window.location.pathname !== '/admin') {
              console.log('User role changed to super_admin, should redirect');
              // We'll let the components handle the redirect
            }
          }
        )
        .subscribe();

      // Less frequent refresh for verification status updates
      const interval = setInterval(() => {
        fetchProfile(true); // Force refresh for periodic updates
      }, 30000); // Every 30 seconds - reduced frequency

      return () => {
        subscription.unsubscribe();
        clearInterval(interval);
      };
    }
  }, [fetchProfile, user, profile?.verification_status, toast]);

  return {
    profile,
    loading,
    isCreatingProfile,
    fetchProfile,
    refreshProfile,
    createProfile,
    updateProfile,
    deleteProfile,
  };
};
