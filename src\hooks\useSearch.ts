import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { PropertyWithImages, SearchFilters } from '@/types/property';

export const useSearch = () => {
  const [results, setResults] = useState<PropertyWithImages[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const { toast } = useToast();

  const searchProperties = useCallback(async (
    filters: SearchFilters,
    page = 0,
    limit = 12,
    sortBy = 'created_at',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('properties')
        .select(`
          id,
          user_id,
          title,
          description,
          rent,
          location,
          county,
          room_type,
          bedrooms,
          bathrooms,
          area,
          amenities,
          available,
          featured,
          latitude,
          longitude,
          formatted_address,
          neighborhood,
          city,
          created_at,
          updated_at,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          ),
          profiles:profiles (
            id,
            full_name,
            phone,
            created_at,
            updated_at
          )
        `, { count: 'exact' })
        .eq('available', true);

      // Apply filters
      if (filters.county) {
        query = query.eq('county', filters.county);
      }
      
      if (filters.location) {
        query = query.ilike('location', `%${filters.location}%`);
      }
      
      if (filters.minRent !== undefined) {
        query = query.gte('rent', filters.minRent);
      }
      
      if (filters.maxRent !== undefined) {
        query = query.lte('rent', filters.maxRent);
      }
      
      if (filters.bedrooms !== undefined) {
        query = query.eq('bedrooms', filters.bedrooms);
      }
      
      if (filters.bathrooms !== undefined) {
        query = query.eq('bathrooms', filters.bathrooms);
      }
      
      if (filters.minArea !== undefined) {
        query = query.gte('area', filters.minArea);
      }
      
      if (filters.maxArea !== undefined) {
        query = query.lte('area', filters.maxArea);
      }
      
      if (filters.featured !== undefined) {
        query = query.eq('featured', filters.featured);
      }
      
      if (filters.amenities && filters.amenities.length > 0) {
        query = query.contains('amenities', filters.amenities);
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      query = query.range(page * limit, (page + 1) * limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      setResults((data as unknown as PropertyWithImages[]) || []);
      setTotalCount(count || 0);

      return {
        data: (data as unknown as PropertyWithImages[]) || [],
        count: count || 0,
        hasMore: count ? (page + 1) * limit < count : false
      };
    } catch (error) {
      toast({
        title: "Error searching properties",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return {
        data: [],
        count: 0,
        hasMore: false
      };
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const searchByText = useCallback(async (
    searchTerm: string,
    page = 0,
    limit = 12
  ) => {
    try {
      setLoading(true);
      
      const { data, error, count } = await supabase
        .from('properties')
        .select(`
          id,
          user_id,
          title,
          description,
          rent,
          location,
          county,
          room_type,
          bedrooms,
          bathrooms,
          area,
          amenities,
          available,
          featured,
          latitude,
          longitude,
          formatted_address,
          neighborhood,
          city,
          created_at,
          updated_at,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          ),
          profiles:profiles (
            id,
            full_name,
            phone,
            created_at,
            updated_at
          )
        `, { count: 'exact' })
        .eq('available', true)
        .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,location.ilike.%${searchTerm}%,county.ilike.%${searchTerm}%`)
        .order('created_at', { ascending: false })
        .range(page * limit, (page + 1) * limit - 1);

      if (error) throw error;

      setResults((data as unknown as PropertyWithImages[]) || []);
      setTotalCount(count || 0);

      return {
        data: (data as unknown as PropertyWithImages[]) || [],
        count: count || 0,
        hasMore: count ? (page + 1) * limit < count : false
      };
    } catch (error) {
      toast({
        title: "Error searching properties",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return {
        data: [],
        count: 0,
        hasMore: false
      };
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const getFeaturedProperties = useCallback(async (limit = 6) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('properties')
        .select(`
          id,
          user_id,
          title,
          description,
          rent,
          location,
          county,
          room_type,
          bedrooms,
          bathrooms,
          area,
          amenities,
          available,
          featured,
          latitude,
          longitude,
          formatted_address,
          neighborhood,
          city,
          created_at,
          updated_at,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          ),
          profiles:profiles (
            id,
            full_name,
            phone,
            created_at,
            updated_at
          )
        `)
        .eq('available', true)
        .eq('featured', true)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return (data as unknown as PropertyWithImages[]) || [];
    } catch (error) {
      toast({
        title: "Error fetching featured properties",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return [];
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const getRecentProperties = useCallback(async (limit = 6) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('properties')
        .select(`
          id,
          user_id,
          title,
          description,
          rent,
          location,
          county,
          room_type,
          bedrooms,
          bathrooms,
          area,
          amenities,
          available,
          featured,
          latitude,
          longitude,
          formatted_address,
          neighborhood,
          city,
          created_at,
          updated_at,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          ),
          profiles:profiles (
            id,
            full_name,
            phone,
            created_at,
            updated_at
          )
        `)
        .eq('available', true)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return (data as unknown as PropertyWithImages[]) || [];
    } catch (error) {
      toast({
        title: "Error fetching recent properties",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return [];
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const getSimilarProperties = useCallback(async (
    propertyId: string,
    county: string,
    rent: number,
    bedrooms: number,
    limit = 4
  ) => {
    try {
      const rentRange = rent * 0.3; // 30% price range
      
      const { data, error } = await supabase
        .from('properties')
        .select(`
          id,
          user_id,
          title,
          description,
          rent,
          location,
          county,
          room_type,
          bedrooms,
          bathrooms,
          area,
          amenities,
          available,
          featured,
          latitude,
          longitude,
          formatted_address,
          neighborhood,
          city,
          created_at,
          updated_at,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          ),
          profiles:profiles (
            id,
            full_name,
            phone,
            created_at,
            updated_at
          )
        `)
        .eq('available', true)
        .eq('county', county)
        .eq('bedrooms', bedrooms)
        .gte('rent', rent - rentRange)
        .lte('rent', rent + rentRange)
        .neq('id', propertyId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return (data as unknown as PropertyWithImages[]) || [];
    } catch (error) {
      console.error('Error fetching similar properties:', error);
      return [];
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults([]);
    setTotalCount(0);
  }, []);

  return {
    results,
    loading,
    totalCount,
    searchProperties,
    searchByText,
    getFeaturedProperties,
    getRecentProperties,
    getSimilarProperties,
    clearResults,
  };
};
