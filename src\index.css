/* Leaflet CSS for maps */
@import 'leaflet/dist/leaflet.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Rental House Finder Design System */

@layer base {
  :root {
    /* Brand Colors */
    --background: 25 15% 98%;
    --foreground: 15 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 15 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 15 20% 15%;

    /* Primary: Warm Orange/Coral */
    --primary: 16 85% 55%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 16 85% 65%;
    --primary-dark: 16 85% 45%;

    /* Secondary: Warm Beige/Sand */
    --secondary: 35 25% 90%;
    --secondary-foreground: 15 20% 25%;

    --muted: 25 15% 95%;
    --muted-foreground: 15 10% 50%;

    /* Accent: Earthy Green */
    --accent: 120 20% 45%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 25 15% 88%;
    --input: 25 15% 95%;
    --ring: 16 85% 55%;

    --radius: 0.75rem;

    /* Custom Design Tokens */
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-light)) 100%);
    --gradient-card: linear-gradient(180deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    --shadow-soft: 0 4px 20px -2px hsl(var(--primary) / 0.1);
    --shadow-card: 0 8px 25px -8px hsl(var(--primary) / 0.15);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark theme with warm tones for rental properties */
    --background: 15 20% 6%;
    --foreground: 35 15% 95%;

    --card: 15 20% 8%;
    --card-foreground: 35 15% 95%;

    --popover: 15 20% 8%;
    --popover-foreground: 35 15% 95%;

    /* Primary: Warm Orange/Coral (softer in dark mode) */
    --primary: 16 75% 60%;
    --primary-foreground: 15 20% 10%;
    --primary-light: 16 75% 70%;
    --primary-dark: 16 75% 50%;

    /* Secondary: Warm Dark Beige */
    --secondary: 25 15% 15%;
    --secondary-foreground: 35 15% 85%;

    --muted: 25 15% 12%;
    --muted-foreground: 35 10% 65%;

    /* Accent: Earthy Green (adjusted for dark mode) */
    --accent: 120 25% 35%;
    --accent-foreground: 35 15% 95%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 35 15% 95%;

    --border: 25 15% 18%;
    --input: 25 15% 15%;
    --ring: 16 75% 60%;

    /* Custom Design Tokens for Dark Mode */
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-light)) 100%);
    --gradient-card: linear-gradient(180deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    --shadow-soft: 0 4px 20px -2px hsl(var(--primary) / 0.2);
    --shadow-card: 0 8px 25px -8px hsl(var(--primary) / 0.25);

    --sidebar-background: 15 20% 8%;
    --sidebar-foreground: 35 15% 85%;
    --sidebar-primary: 16 75% 60%;
    --sidebar-primary-foreground: 15 20% 10%;
    --sidebar-accent: 25 15% 12%;
    --sidebar-accent-foreground: 35 15% 85%;
    --sidebar-border: 25 15% 18%;
    --sidebar-ring: 16 75% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-background text-foreground transition-colors duration-300;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Dark mode image adjustments */
  .dark img {
    @apply brightness-90;
    filter: brightness(0.9) contrast(1.1);
  }

  .dark img:hover {
    @apply brightness-100;
    filter: brightness(1) contrast(1);
  }

  /* Mobile-first optimizations */
  html {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  /* Improved touch targets for mobile */
  button, [role="button"], input[type="button"], input[type="submit"] {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Better text readability on mobile */
  @media (max-width: 640px) {
    body {
      @apply text-sm;
    }
    
    h1 {
      @apply text-xl;
    }
    
    h2 {
      @apply text-lg;
    }
    
    h3 {
      @apply text-base;
    }
  }

  /* Scrollbar styling with dark mode support */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Dark mode scrollbar */
  .dark ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/40;
  }
  
  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/60;
  }
}