import { supabase } from '@/integrations/supabase/client';

/**
 * Get a valid signed URL for a verification document
 * Since verification-documents bucket is private, we need signed URLs for access
 */
export const getDocumentUrl = async (documentUrl: string): Promise<string> => {
  try {
    // If the URL is already a valid full URL and not expired, return it
    if (documentUrl.startsWith('http') && !documentUrl.includes('token=')) {
      return documentUrl;
    }

    // Extract file path from URL
    let filePath = documentUrl;
    if (filePath.includes('/storage/v1/object/')) {
      // Extract path after the bucket name
      const parts = filePath.split('/storage/v1/object/');
      if (parts[1]) {
        const pathParts = parts[1].split('/');
        if (pathParts.length > 1) {
          filePath = pathParts.slice(1).join('/'); // Remove bucket name
        }
      }
    }

    // Generate signed URL for private bucket (valid for 1 hour)
    const { data, error } = await supabase.storage
      .from('verification-documents')
      .createSignedUrl(filePath, 3600); // 1 hour expiry

    if (error) {
      console.error('Error creating signed URL:', error);
      return documentUrl; // Return original as fallback
    }

    return data.signedUrl;
  } catch (error) {
    console.error('Error generating document URL:', error);
    return documentUrl; // Return original as fallback
  }
};

/**
 * Regenerate a signed URL from a file path
 * Useful for fixing broken URLs in the database
 */
export const regenerateDocumentUrl = async (filePath: string): Promise<string> => {
  try {
    // Remove any existing base URL if present
    let cleanPath = filePath;
    if (filePath.includes('/storage/v1/object/public/verification-documents/')) {
      cleanPath = filePath.split('/storage/v1/object/public/verification-documents/')[1];
    } else if (filePath.includes('/storage/v1/object/sign/verification-documents/')) {
      cleanPath = filePath.split('/storage/v1/object/sign/verification-documents/')[1];
      // Remove query parameters if present
      cleanPath = cleanPath.split('?')[0];
    } else if (filePath.includes('verification-documents/')) {
      cleanPath = filePath.split('verification-documents/')[1];
    }

    // Generate signed URL for private bucket (valid for 1 hour)
    const { data, error } = await supabase.storage
      .from('verification-documents')
      .createSignedUrl(cleanPath, 3600);

    if (error) {
      console.error('Error regenerating signed URL:', error);
      return filePath;
    }

    return data.signedUrl;
  } catch (error) {
    console.error('Error regenerating document URL:', error);
    return filePath;
  }
};

/**
 * Validate if a document URL is accessible
 * This is useful for debugging broken URLs
 */
export const validateDocumentUrl = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('Error validating document URL:', error);
    return false;
  }
};
