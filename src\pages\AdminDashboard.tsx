import React, { useEffect, useState, lazy, Suspense, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { useProfile } from '@/hooks/useProfile';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { DarkModeToggle } from '@/components/DarkModeToggle';
import { Shield, AlertTriangle, ArrowLeft } from 'lucide-react';
import { LoadingSkeleton } from '@/components/ui/loading-skeleton';
import { performanceMonitor } from '@/utils/performance';

// Lazy load the heavy admin dashboard component
const AdminVerificationDashboard = lazy(() => import('@/components/admin/AdminVerificationDashboard').then(module => ({ default: module.AdminVerificationDashboard })));

const AdminDashboard: React.FC = () => {
  const { user, signOut } = useAuth();
  const { profile } = useProfile();
  const navigate = useNavigate();
  const location = useLocation();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialTab, setInitialTab] = useState('overview');

  // Memoized sign out handler
  const handleSignOut = useCallback(async () => {
    performanceMonitor.startTimer('admin-signout');
    const { error } = await signOut();
    performanceMonitor.endTimer('admin-signout');

    if (error) {
      console.error('Sign out error:', error);
    } else {
      navigate('/');
    }
  }, [signOut, navigate]);

  // Memoized admin access check
  const checkAdminAccess = useCallback(async () => {
    if (!user) {
      navigate('/auth');
      return;
    }

    if (!profile) {
      setLoading(true);
      return;
    }

    performanceMonitor.startTimer('admin-access-check');

    // Check if user has admin role
    const hasAdminAccess = profile.user_role === 'admin' || profile.user_role === 'super_admin';
    setIsAdmin(hasAdminAccess);
    setLoading(false);

    performanceMonitor.endTimer('admin-access-check');

    if (!hasAdminAccess) {
      // Redirect non-admin users after a delay
      setTimeout(() => {
        navigate('/');
      }, 3000);
    }
  }, [user, profile, navigate]);

  useEffect(() => {
    checkAdminAccess();

    // Check for hash navigation
    if (location.hash === '#landlords') {
      setInitialTab('landlords');
    }

    checkAdminAccess();
  }, [checkAdminAccess, location.hash]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <LoadingSkeleton showAvatar lines={3} />
            <p className="text-muted-foreground mt-4">Checking admin permissions...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isAdmin === false) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center space-y-6">
            <AlertTriangle className="h-16 w-16 mx-auto text-destructive" />
            <div>
              <h2 className="text-2xl font-bold text-foreground mb-2">Access Denied</h2>
              <p className="text-muted-foreground">
                You don't have permission to access the admin dashboard.
              </p>
            </div>
            
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                This area is restricted to administrators only. If you believe this is an error, 
                please contact your system administrator.
              </AlertDescription>
            </Alert>

            <div className="space-y-3">
              <Button 
                onClick={() => navigate('/')} 
                className="w-full"
                variant="outline"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Return to Home
              </Button>
              
              <Button 
                onClick={handleSignOut} 
                variant="destructive"
                className="w-full"
              >
                Sign Out
              </Button>
            </div>

            <p className="text-xs text-muted-foreground">
              Redirecting to home page in 3 seconds...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Admin Header */}
      <div className="bg-card border-b border-border sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Shield className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-xl font-bold text-foreground">Admin Dashboard</h1>
                <p className="text-sm text-muted-foreground">
                  Verification & Security Management
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm font-medium">{profile?.full_name}</p>
                <p className="text-xs text-muted-foreground capitalize">
                  {profile?.user_role?.replace('_', ' ')}
                </p>
              </div>
              
              <div className="flex items-center gap-2">
                <DarkModeToggle />
                
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigate('/')}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Site
                </Button>
                
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleSignOut}
                >
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Admin Content */}
      <div className="container mx-auto px-4 py-8">
        <Suspense fallback={
          <div className="space-y-6">
            <LoadingSkeleton lines={1} className="h-8 w-48" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <LoadingSkeleton lines={3} />
                  </CardContent>
                </Card>
              ))}
            </div>
            <LoadingSkeleton lines={8} />
          </div>
        }>
          <AdminVerificationDashboard initialTab={initialTab} />
        </Suspense>
      </div>
    </div>
  );
};

export default AdminDashboard;
