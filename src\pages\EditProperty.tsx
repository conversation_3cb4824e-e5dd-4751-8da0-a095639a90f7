import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useProperties } from '@/hooks/useProperties';
import { useAuth } from '@/hooks/useAuth';
import { PropertyFormData, ROOM_TYPES } from '@/types/property';
import { Save, ArrowLeft, Upload, X, Star } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import LocationInput from '@/components/LocationInput';
import Navigation from '@/components/Navigation';

const KENYAN_COUNTIES = [
  'Nairobi', 'Mombasa', 'Kisu<PERSON>', 'Nakuru', 'Eldoret', 'Thika', 'Machakos', 'Meru',
  'Nyeri', 'Garissa', 'Kakamega', 'Kitale', 'Malindi', 'Lamu', 'Wajir', 'Marsabit',
  'Isiolo', 'Moyale', 'Lodwar', 'Kapenguria', 'Bungoma', 'Webuye', 'Busia',
  'Migori', 'Kisii', 'Kericho', 'Bomet', 'Narok', 'Kajiado', 'Kiambu', 'Murang\'a',
  'Kirinyaga', 'Nanyuki', 'Embu', 'Mwingi', 'Kitui', 'Makueni',
  'Kilifi', 'Kwale', 'Taita Taveta', 'Tana River', 'Mandera', 'Turkana', 'Samburu',
  'Laikipia', 'Baringo', 'Elgeyo Marakwet', 'Nandi', 'Trans Nzoia', 'Uasin Gishu',
  'West Pokot', 'Vihiga', 'Siaya', 'Homa Bay', 'Nyamira'
];

const COMMON_AMENITIES = [
  'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Balcony',
  'Air Conditioning', 'Heating', 'Internet/WiFi', 'Cable TV', 'Laundry',
  'Elevator', 'Backup Generator', 'Water Tank', 'CCTV', 'Gated Community'
];

const EditProperty: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const { updateProperty, uploadPropertyImage, deletePropertyImage } = useProperties();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<PropertyFormData>({
    title: '',
    description: '',
    rent: 0,
    location: '',
    county: '',
    room_type: '',
    bedrooms: 1,
    bathrooms: 1,
    area: 0,
    amenities: [],
    available: true,
    featured: false,
    latitude: undefined,
    longitude: undefined,
    formatted_address: undefined,
    neighborhood: undefined,
    city: undefined
  });
  
  const [loading, setLoading] = useState(false);
  const [fetchingProperty, setFetchingProperty] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Image management state
  const [existingImages, setExistingImages] = useState<{id: string, image_url: string, is_primary: boolean}[]>([]);
  const [newImages, setNewImages] = useState<File[]>([]);
  const [newImagePreviews, setNewImagePreviews] = useState<string[]>([]);
  const [uploadingImages, setUploadingImages] = useState(false);

  // Fetch property data on component mount
  useEffect(() => {
    const fetchProperty = async () => {
      if (!id || !user) {
        navigate('/my-properties');
        return;
      }

      try {
        const { data, error } = await supabase
          .from('properties')
          .select(`
            *,
            property_images(*)
          `)
          .eq('id', id)
          .eq('user_id', user.id)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            toast({
              title: "Property not found",
              description: "The property you're trying to edit doesn't exist or you don't have permission to edit it.",
              variant: "destructive",
            });
          } else {
            throw error;
          }
          navigate('/my-properties');
          return;
        }

        // Populate form with existing data
        setFormData({
          title: data.title || '',
          description: data.description || '',
          rent: data.rent || 0,
          location: data.location || '',
          county: data.county || '',
          room_type: data.room_type || '',
          bedrooms: data.bedrooms || 1,
          bathrooms: data.bathrooms || 1,
          area: data.area || 0,
          amenities: data.amenities || [],
          available: data.available ?? true,
          featured: data.featured ?? false,
          latitude: data.latitude || undefined,
          longitude: data.longitude || undefined,
          formatted_address: data.formatted_address || undefined,
          neighborhood: data.neighborhood || undefined,
          city: data.city || undefined
        });

        // Set existing images
        setExistingImages(data.property_images || []);
      } catch (error) {
        console.error('Error fetching property:', error);
        toast({
          title: "Error loading property",
          description: "Failed to load property data. Please try again.",
          variant: "destructive",
        });
        navigate('/my-properties');
      } finally {
        setFetchingProperty(false);
      }
    };

    fetchProperty();
  }, [id, user, navigate, toast]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      navigate('/auth');
    }
  }, [user, navigate]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }
    if (!formData.county) {
      newErrors.county = 'County is required';
    }
    if (!formData.room_type) {
      newErrors.room_type = 'Room type is required';
    }
    if (formData.rent <= 0) {
      newErrors.rent = 'Rent must be greater than 0';
    }
    if (formData.area <= 0) {
      newErrors.area = 'Area must be greater than 0';
    }
    if (formData.bedrooms < 1) {
      newErrors.bedrooms = 'Bedrooms must be at least 1';
    }
    if (formData.bathrooms < 1) {
      newErrors.bathrooms = 'Bathrooms must be at least 1';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAmenityChange = (amenity: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      amenities: checked 
        ? [...prev.amenities, amenity]
        : prev.amenities.filter(a => a !== amenity)
    }));
  };

  // Image management functions
  const handleNewImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    if (files.length + newImages.length + existingImages.length > 10) {
      toast({
        title: "Too many images",
        description: "Maximum 10 images allowed per property",
        variant: "destructive",
      });
      return;
    }

    setNewImages(prev => [...prev, ...files]);
    
    // Create previews for new images
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeNewImage = (index: number) => {
    setNewImages(prev => prev.filter((_, i) => i !== index));
    setNewImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const deleteExistingImage = async (imageId: string) => {
    if (!confirm("Are you sure you want to delete this image?")) return;

    const success = await deletePropertyImage(imageId);
    if (success) {
      setExistingImages(prev => prev.filter(img => img.id !== imageId));
    }
  };

  const uploadNewImages = async () => {
    if (!id || newImages.length === 0) return true; // Return true if no images to upload

    setUploadingImages(true);
    try {
      let uploadedCount = 0;
      for (let i = 0; i < newImages.length; i++) {
        const image = newImages[i];
        const isPrimary = existingImages.length === 0 && i === 0; // First image is primary if no existing images
        
        const result = await uploadPropertyImage(id, image, isPrimary);
        if (result) {
          setExistingImages(prev => [...prev, result]);
          uploadedCount++;
        }
      }
      
      // Clear new images after successful upload
      setNewImages([]);
      setNewImagePreviews([]);
      
      return uploadedCount === newImages.length; // Return true only if all images uploaded successfully
    } catch (error) {
      console.error('Error uploading images:', error);
      toast({
        title: "Error uploading images",
        description: "Some images failed to upload. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setUploadingImages(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!id || !validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      // First, upload any new images if there are any
      if (newImages.length > 0) {
        const imageUploadSuccess = await uploadNewImages();
        if (!imageUploadSuccess) {
          toast({
            title: "Upload incomplete",
            description: "Some images failed to upload. Please check and try again.",
            variant: "destructive",
          });
          return;
        }
      }
      
      // Then update the property
      const result = await updateProperty(id, formData);
      
      if (result) {
        toast({
          title: "Property updated successfully",
          description: "Your property and images have been updated.",
        });
        navigate('/my-properties');
      }
    } catch (error) {
      console.error('Error updating property:', error);
      toast({
        title: "Error updating property",
        description: "Failed to update property. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof PropertyFormData, value: string | number | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!user) {
    return null; // Will redirect
  }

  if (fetchingProperty) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto text-center">
          <p>Loading property data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" onClick={() => navigate('/my-properties')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to My Properties
              </Button>
            </div>
            <CardTitle>Edit Property</CardTitle>
            <CardDescription>
              Update your property details
            </CardDescription>
          </CardHeader>
          
          <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Property Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="e.g., 2 Bedroom Apartment in Kilimani"
                  className={errors.title ? 'border-red-500' : ''}
                />
                {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
              </div>
            </div>

            {/* Location */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <LocationInput
                  value={formData.location}
                  onChange={(value, coordinates) => {
                    handleInputChange('location', value);
                    if (coordinates) {
                      setFormData(prev => ({
                        ...prev,
                        latitude: coordinates.lat,
                        longitude: coordinates.lng
                      }));
                    }
                  }}
                  label="Location *"
                  placeholder="e.g., Kilimani, Nairobi"
                  error={errors.location}
                  coordinates={formData.latitude && formData.longitude ? {
                    lat: formData.latitude,
                    lng: formData.longitude
                  } : undefined}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="county">County *</Label>
                <Select
                  value={formData.county}
                  onValueChange={(value) => handleInputChange('county', value)}
                >
                  <SelectTrigger className={errors.county ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select County" />
                  </SelectTrigger>
                  <SelectContent>
                    {KENYAN_COUNTIES.map(county => (
                      <SelectItem key={county} value={county}>
                        {county}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.county && <p className="text-sm text-red-500">{errors.county}</p>}
              </div>
            </div>

            {/* Room Type */}
            <div className="space-y-2">
              <Label htmlFor="room_type">Room Type *</Label>
              <Select
                value={formData.room_type}
                onValueChange={(value) => handleInputChange('room_type', value)}
              >
                <SelectTrigger className={errors.room_type ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select Room Type" />
                </SelectTrigger>
                <SelectContent>
                  {ROOM_TYPES.map(roomType => (
                    <SelectItem key={roomType.value} value={roomType.value}>
                      {roomType.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.room_type && <p className="text-sm text-red-500">{errors.room_type}</p>}
            </div>

            {/* Property Details */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="rent">Monthly Rent (KES) *</Label>
                <Input
                  id="rent"
                  type="number"
                  value={formData.rent}
                  onChange={(e) => handleInputChange('rent', parseFloat(e.target.value) || 0)}
                  placeholder="50000"
                  className={errors.rent ? 'border-red-500' : ''}
                />
                {errors.rent && <p className="text-sm text-red-500">{errors.rent}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="bedrooms">Bedrooms *</Label>
                <Input
                  id="bedrooms"
                  type="number"
                  min="1"
                  value={formData.bedrooms}
                  onChange={(e) => handleInputChange('bedrooms', parseInt(e.target.value) || 1)}
                  className={errors.bedrooms ? 'border-red-500' : ''}
                />
                {errors.bedrooms && <p className="text-sm text-red-500">{errors.bedrooms}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="bathrooms">Bathrooms *</Label>
                <Input
                  id="bathrooms"
                  type="number"
                  min="1"
                  value={formData.bathrooms}
                  onChange={(e) => handleInputChange('bathrooms', parseInt(e.target.value) || 1)}
                  className={errors.bathrooms ? 'border-red-500' : ''}
                />
                {errors.bathrooms && <p className="text-sm text-red-500">{errors.bathrooms}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="area">Area (sq ft) *</Label>
                <Input
                  id="area"
                  type="number"
                  value={formData.area}
                  onChange={(e) => handleInputChange('area', parseFloat(e.target.value) || 0)}
                  placeholder="1200"
                  className={errors.area ? 'border-red-500' : ''}
                />
                {errors.area && <p className="text-sm text-red-500">{errors.area}</p>}
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your property..."
                rows={4}
              />
            </div>

            {/* Amenities */}
            <div className="space-y-2">
              <Label>Amenities</Label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {COMMON_AMENITIES.map(amenity => (
                  <div key={amenity} className="flex items-center space-x-2">
                    <Checkbox
                      id={amenity}
                      checked={formData.amenities.includes(amenity)}
                      onCheckedChange={(checked) => 
                        handleAmenityChange(amenity, checked as boolean)
                      }
                    />
                    <Label htmlFor={amenity} className="text-sm">
                      {amenity}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Availability */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="available"
                  checked={formData.available}
                  onCheckedChange={(checked) => handleInputChange('available', checked)}
                />
                <Label htmlFor="available">
                  Property is currently available for rent
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="featured"
                  checked={formData.featured}
                  onCheckedChange={(checked) => handleInputChange('featured', checked)}
                />
                <Label htmlFor="featured">
                  Feature this property (highlighted in searches)
                </Label>
              </div>
            </div>

            {/* Image Management */}
            <div className="space-y-4">
              <Label>Property Images</Label>
              
              {/* Existing Images */}
              {existingImages.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">Current Images</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {existingImages.map((image, index) => (
                      <div key={image.id} className="relative">
                        <img
                          src={image.image_url}
                          alt={`Property image ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        {image.is_primary && (
                          <div className="absolute top-2 left-2">
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          </div>
                        )}
                        <button
                          type="button"
                          onClick={() => deleteExistingImage(image.id)}
                          className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* New Image Upload */}
              <div>
                <h4 className="text-sm font-medium mb-2">Add New Images</h4>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                  <div className="text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                      <label htmlFor="new-images" className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-900">
                          Click to upload new images
                        </span>
                        <span className="block text-sm text-gray-500">
                          PNG, JPG, GIF up to 10MB each (Max {10 - existingImages.length} more images)
                        </span>
                      </label>
                      <input
                        id="new-images"
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleNewImageUpload}
                        className="hidden"
                        disabled={existingImages.length >= 10}
                      />
                    </div>
                  </div>
                </div>

                {/* New Image Previews */}
                {newImagePreviews.length > 0 && (
                  <div className="mt-4">
                    <h5 className="text-sm font-medium mb-2">New Images to Upload</h5>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {newImagePreviews.map((preview, index) => (
                        <div key={index} className="relative">
                          <img
                            src={preview}
                            alt={`New image ${index + 1}`}
                            className="w-full h-32 object-cover rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() => removeNewImage(index)}
                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                    <Button
                      type="button"
                      onClick={uploadNewImages}
                      disabled={uploadingImages || newImages.length === 0}
                      className="mt-2"
                      size="sm"
                    >
                      {uploadingImages ? (
                        <>
                          <Upload className="mr-2 h-4 w-4 animate-spin" />
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Upload className="mr-2 h-4 w-4" />
                          Upload New Images
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex gap-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/my-properties')}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <Save className="mr-2 h-4 w-4 animate-spin" />
                    Updating Property...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Update Property
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      </div>
    </div>
  );
};

export default EditProperty;
