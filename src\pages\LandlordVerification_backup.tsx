import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  CheckCircle, 
  Clock, 
  XCircle, 
  AlertTriangle,
  Star,
  Award,
  ArrowRight,
  Home,
  ChevronRight,
  ArrowLeft
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useProfile } from '@/hooks/useProfile';
import { useVerification } from '@/hooks/useVerification';
import { LandlordVerificationForm } from '@/components/verification/LandlordVerificationForm';
import { TrustIndicators } from '@/components/verification/TrustIndicators';
import Navigation from '@/components/Navigation';
import type { TrustIndicators as TrustIndicatorsType } from '@/types/verification';

const LandlordVerification: React.FC = () => {
  const { user } = useAuth();
  const { profile, loading: profileLoading } = useProfile();
  const { verificationDocuments, getTrustIndicators } = useVerification();
  const navigate = useNavigate();
  const [trustData, setTrustData] = useState<TrustIndicatorsType | null>(null);
  const [showVerificationForm, setShowVerificationForm] = useState(false);

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }

    // Load trust indicators
    const loadTrustData = async () => {
      const data = await getTrustIndicators(user.id);
      setTrustData(data);
    };

    loadTrustData();
  }, [user, navigate, getTrustIndicators]);

  if (!user || !profile) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-pulse text-center">
          <Shield className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-muted-foreground">Loading verification status...</p>
        </div>
      </div>
    );
  }

  const getVerificationStatusInfo = () => {
    switch (profile.verification_status) {
      case 'verified':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          title: 'Verification Complete',
          description: 'Your landlord account has been successfully verified.',
          action: null
        };
      case 'under_review':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          title: 'Under Review',
          description: 'Your verification application is being reviewed by our team.',
          action: null
        };
      case 'rejected':
        return {
          icon: XCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          title: 'Verification Rejected',
          description: 'Your verification application was rejected. Please review the feedback and resubmit.',
          action: 'Resubmit Application'
        };
      case 'suspended':
        return {
          icon: AlertTriangle,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          title: 'Account Suspended',
          description: 'Your account has been suspended. Please contact support for assistance.',
          action: null
        };
      default:
        return {
          icon: Shield,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          title: 'Verification Required',
          description: 'Complete your landlord verification to build trust with potential tenants.',
          action: 'Start Verification'
        };
    }
  };

  const statusInfo = getVerificationStatusInfo();
  const StatusIcon = statusInfo.icon;

  const getVerificationProgress = () => {
    if (profile.verification_status === 'verified') return 100;
    if (profile.verification_status === 'under_review') return 80;
    if (verificationDocuments.length > 0) return 60;
    if (profile.verification_submitted_at) return 40;
    return 0;
  };

  const verificationBenefits = [
    {
      icon: Shield,
      title: 'Build Trust',
      description: 'Verified badge increases tenant confidence'
    },
    {
      icon: Star,
      title: 'Higher Rankings',
      description: 'Verified properties appear higher in search results'
    },
    {
      icon: Award,
      title: 'Premium Features',
      description: 'Access to advanced landlord tools and analytics'
    },
    {
      icon: CheckCircle,
      title: 'Faster Rentals',
      description: 'Verified landlords rent properties 3x faster'
    }
  ];

  if (showVerificationForm) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          {/* Navigation Header */}
          <div className="mb-6">
            <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-4">
              <button 
                onClick={() => navigate('/')}
                className="hover:text-primary transition-colors"
              >
                Home
              </button>
              <ChevronRight className="h-4 w-4" />
              <button 
                onClick={() => navigate('/profile')}
                className="hover:text-primary transition-colors"
              >
                Profile
              </button>
              <ChevronRight className="h-4 w-4" />
              <span className="text-foreground font-medium">Verification</span>
            </nav>
            <Button 
              variant="outline" 
              onClick={() => setShowVerificationForm(false)}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Overview
            </Button>
          </div>
          <LandlordVerificationForm 
            onVerificationSubmitted={() => {
              setShowVerificationForm(false);
              // The real-time subscription will automatically update the status
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Navigation Header */}
        <div className="mb-6">
          <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
            <button 
              onClick={() => navigate('/')}
              className="hover:text-primary transition-colors"
            >
              Home
            </button>
            <ChevronRight className="h-4 w-4" />
            <button 
              onClick={() => navigate('/profile')}
              className="hover:text-primary transition-colors"
            >
              Profile
            </button>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground font-medium">Verification</span>
          </nav>
        </div>

        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full ${statusInfo.bgColor}`}>
              <StatusIcon className={`h-8 w-8 ${statusInfo.color}`} />
            </div>
          </div>
          <h1 className="text-3xl font-bold">{statusInfo.title}</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            {statusInfo.description}
          </p>
          {profile.verification_status === 'pending' && (
            <Alert className="max-w-2xl mx-auto">
              <Clock className="h-4 w-4" />
              <AlertDescription>
                <strong>Status Update:</strong> Your verification status will be updated automatically when changes occur in the system.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Verification Progress */}
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Verification Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{getVerificationProgress()}%</span>
              </div>
              <Progress value={getVerificationProgress()} className="h-2" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
              <div className={`p-3 rounded-lg ${getVerificationProgress() >= 25 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                <div className="text-sm font-medium">Account Created</div>
                <CheckCircle className="h-4 w-4 mx-auto mt-1" />
              </div>
              <div className={`p-3 rounded-lg ${getVerificationProgress() >= 50 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                <div className="text-sm font-medium">Info Submitted</div>
                {getVerificationProgress() >= 50 ? <CheckCircle className="h-4 w-4 mx-auto mt-1" /> : <Clock className="h-4 w-4 mx-auto mt-1" />}
              </div>
              <div className={`p-3 rounded-lg ${getVerificationProgress() >= 75 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                <div className="text-sm font-medium">Under Review</div>
                {getVerificationProgress() >= 75 ? <CheckCircle className="h-4 w-4 mx-auto mt-1" /> : <Clock className="h-4 w-4 mx-auto mt-1" />}
              </div>
              <div className={`p-3 rounded-lg ${getVerificationProgress() >= 100 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                <div className="text-sm font-medium">Verified</div>
                {getVerificationProgress() >= 100 ? <CheckCircle className="h-4 w-4 mx-auto mt-1" /> : <Clock className="h-4 w-4 mx-auto mt-1" />}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Status */}
        <div className="max-w-4xl mx-auto">
          {profile.verification_status === 'verified' && trustData && (
            <Card>
              <CardHeader>
                <CardTitle>Your Verification Status</CardTitle>
                <CardDescription>
                  Your landlord account is fully verified and trusted
                </CardDescription>
              </CardHeader>
              <CardContent>
                <TrustIndicators trustData={trustData} showDetailed={true} />
              </CardContent>
            </Card>
          )}

          {profile.verification_status === 'under_review' && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                <strong>Review in Progress:</strong> Our team is reviewing your verification documents. 
                This typically takes 2-3 business days. We'll notify you once the review is complete.
              </AlertDescription>
            </Alert>
          )}

          {profile.verification_status === 'rejected' && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Application Rejected:</strong> {profile.admin_notes || 'Please review your documents and resubmit your application.'}
              </AlertDescription>
            </Alert>
          )}

          {profile.verification_status === 'suspended' && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Account Suspended:</strong> Your account has been suspended due to policy violations. 
                Please contact our support team for assistance.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Action Button */}
        {statusInfo.action && (
          <div className="text-center">
            <Button 
              onClick={() => setShowVerificationForm(true)}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Shield className="h-5 w-5 mr-2" />
              {statusInfo.action}
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>
          </div>
        )}

        {/* Verification Benefits */}
        {profile.verification_status !== 'verified' && (
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-center">Why Get Verified?</CardTitle>
                <CardDescription className="text-center">
                  Verified landlords enjoy these exclusive benefits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {verificationBenefits.map((benefit, index) => {
                    const Icon = benefit.icon;
                    return (
                      <div key={index} className="flex items-start gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Icon className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-medium">{benefit.title}</h3>
                          <p className="text-sm text-muted-foreground">{benefit.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Quick Actions */}
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button 
                  variant="outline" 
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => navigate('/my-properties')}
                >
                  <Home className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">Manage Properties</div>
                    <div className="text-sm text-muted-foreground">
                      View and edit your listings
                    </div>
                  </div>
                </Button>

                <Button 
                  variant="outline" 
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => navigate('/profile')}
                >
                  <Shield className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">Update Profile</div>
                    <div className="text-sm text-muted-foreground">
                      Edit your account information
                    </div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LandlordVerification;
