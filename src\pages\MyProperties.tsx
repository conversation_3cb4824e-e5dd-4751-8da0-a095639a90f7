import { useEffect, useState, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Edit, Trash2, Plus, MapPin, Bed, Bath, Square, Shield, ArrowRight } from "lucide-react";
import Navigation from "@/components/Navigation";
import { useAuth } from "@/hooks/useAuth";
import { useProfile } from "@/hooks/useProfile";
import { supabase } from "@/integrations/supabase/client";
import { PropertyWithImages } from "@/types/property";
import { useToast } from "@/hooks/use-toast";
import { LoadingSkeleton } from "@/components/ui/loading-skeleton";
import { performanceMonitor } from "@/utils/performance";

const MyProperties = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useProfile();
  const { toast } = useToast();
  const [properties, setProperties] = useState<PropertyWithImages[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  // Cache duration: 2 minutes for properties (they change less frequently)
  const CACHE_DURATION = 2 * 60 * 1000;

  const fetchMyProperties = useCallback(async (forceRefresh = false) => {
    if (!user) return;

    // Check cache validity
    const now = Date.now();
    const isCacheValid = properties.length > 0 && (now - lastFetchTime) < CACHE_DURATION;

    if (!forceRefresh && isCacheValid) {
      console.log('Using cached properties data');
      setLoading(false);
      return;
    }

    try {
      performanceMonitor.startTimer('my-properties-fetch');

      const { data, error } = await supabase
        .from('properties')
        .select(`
          *,
          property_images(*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      performanceMonitor.endTimer('my-properties-fetch');

      if (error) throw error;

      const formattedProperties = data || [];
      setProperties(formattedProperties);
      setLastFetchTime(now);
    } catch (error) {
      toast({
        title: "Error fetching properties",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user, toast, properties.length, lastFetchTime, CACHE_DURATION]);

  const deleteProperty = useCallback(async (propertyId: string) => {
    if (!confirm("Are you sure you want to delete this property?")) return;

    try {
      performanceMonitor.startTimer('property-delete');

      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', propertyId)
        .eq('user_id', user?.id);

      performanceMonitor.endTimer('property-delete');

      if (error) throw error;

      toast({
        title: "Property deleted",
        description: "Your property has been removed successfully",
      });

      // Optimistically update the UI
      setProperties(prev => prev.filter(p => p.id !== propertyId));
      setLastFetchTime(0); // Invalidate cache
    } catch (error) {
      toast({
        title: "Error deleting property",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  }, [user?.id, toast]);

  const toggleAvailability = useCallback(async (propertyId: string, currentStatus: boolean) => {
    try {
      performanceMonitor.startTimer('property-toggle-availability');

      const { error } = await supabase
        .from('properties')
        .update({ available: !currentStatus })
        .eq('id', propertyId)
        .eq('user_id', user?.id);

      performanceMonitor.endTimer('property-toggle-availability');

      if (error) throw error;

      toast({
        title: "Property updated",
        description: `Property marked as ${!currentStatus ? 'available' : 'unavailable'}`,
      });

      // Optimistically update the UI
      setProperties(prev => prev.map(p =>
        p.id === propertyId ? { ...p, available: !currentStatus } : p
      ));
      setLastFetchTime(0); // Invalidate cache for next fetch
    } catch (error) {
      toast({
        title: "Error updating property",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      // Revert optimistic update on error
      fetchMyProperties(true);
    }
  }, [user?.id, toast, fetchMyProperties]);

  // Memoized property cards to prevent unnecessary re-renders
  const propertyCards = useMemo(() => {
    return properties.map((property) => (
      <Card key={property.id} className="overflow-hidden hover:shadow-lg transition-shadow">
        <div className="relative">
          {property.property_images && property.property_images.length > 0 ? (
            <img
              src={property.property_images[0].image_url}
              alt={property.title}
              className="w-full h-48 object-cover"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500">No image</span>
            </div>
          )}
          <Badge
            className={`absolute top-2 right-2 ${
              property.available
                ? 'bg-green-500 hover:bg-green-600'
                : 'bg-red-500 hover:bg-red-600'
            }`}
          >
            {property.available ? 'Available' : 'Unavailable'}
          </Badge>
        </div>

        <CardHeader className="pb-2">
          <CardTitle className="text-lg line-clamp-1">{property.title}</CardTitle>
          <div className="flex items-center text-sm text-muted-foreground">
            <MapPin className="h-4 w-4 mr-1" />
            <span className="line-clamp-1">{property.location}, {property.county}</span>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="flex items-center justify-between mb-4">
            <div className="text-2xl font-bold text-primary">
              KSh {property.rent.toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">per month</div>
          </div>

          <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
            <div className="flex items-center">
              <Bed className="h-4 w-4 mr-1" />
              {property.bedrooms}
            </div>
            <div className="flex items-center">
              <Bath className="h-4 w-4 mr-1" />
              {property.bathrooms}
            </div>
            <div className="flex items-center">
              <Square className="h-4 w-4 mr-1" />
              {property.area} sq ft
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/edit-property/${property.id}`)}
              className="flex-1"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleAvailability(property.id, property.available)}
              className="flex-1"
            >
              {property.available ? 'Mark Unavailable' : 'Mark Available'}
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => deleteProperty(property.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    ));
  }, [properties, navigate, toggleAvailability, deleteProperty]);

  useEffect(() => {
    if (user) {
      fetchMyProperties();
    }
  }, [user, fetchMyProperties]);

  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-4">Authentication Required</h2>
              <p className="text-muted-foreground mb-4">Please log in to view your properties.</p>
              <Button onClick={() => navigate('/auth')}>Sign In</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center gap-4 mb-6">
            <LoadingSkeleton lines={1} className="h-8 w-20" />
            <LoadingSkeleton lines={1} className="h-8 w-48" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <LoadingSkeleton lines={1} className="h-48 w-full mb-4" />
                <LoadingSkeleton lines={2} />
                <div className="flex justify-between items-center mt-4">
                  <LoadingSkeleton lines={1} className="h-6 w-24" />
                  <LoadingSkeleton lines={1} className="h-8 w-16" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-6 sm:py-8">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={() => navigate('/')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Back</span>
              <span className="sm:hidden">Back</span>
            </Button>
            <h1 className="text-xl sm:text-2xl font-bold">My Properties</h1>
          </div>
          <Button onClick={() => navigate('/add-property')} className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Add Property</span>
            <span className="sm:hidden">Add New</span>
          </Button>
        </div>

        {/* Verification Alert for Landlords */}
        {profile?.user_role === 'landlord' && profile?.verification_status !== 'verified' && (
          <Alert className="mb-6">
            <Shield className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <div>
                <strong>Verify your account</strong> to build trust with tenants and get more inquiries.
              </div>
              <Button 
                size="sm" 
                onClick={() => navigate('/verification')}
                className="ml-4 bg-blue-600 hover:bg-blue-700"
              >
                Get Verified
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {properties.length === 0 ? (
          <Card className="max-w-md mx-auto">
            <CardContent className="p-4 sm:p-6 text-center">
              <h2 className="text-lg sm:text-xl font-semibold mb-4">No properties yet</h2>
              <p className="text-sm sm:text-base text-muted-foreground mb-4">
                Start by adding your first property listing.
              </p>
              <Button onClick={() => navigate('/add-property')} className="w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Property
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
            {propertyCards}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyProperties;