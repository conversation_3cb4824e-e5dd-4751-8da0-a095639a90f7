import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useProfile } from '@/hooks/useProfile';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, RefreshCw, Shield, User, CheckCircle } from 'lucide-react';
import Navigation from '@/components/Navigation';
import { Profile } from '@/types/property';

const ProfileDebug = () => {
  const { user } = useAuth();
  const { profile, refreshProfile, loading } = useProfile();
  const navigate = useNavigate();
  const [dbProfile, setDbProfile] = useState<Profile | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchDirectFromDB = useCallback(async () => {
    if (!user) return;
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (error) {
        console.error('Error fetching from DB:', error);
        return;
      }
      
      setDbProfile(data);
    } catch (error) {
      console.error('Error:', error);
    }
  }, [user]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshProfile();
    await fetchDirectFromDB();
    setIsRefreshing(false);
  };

  useEffect(() => {
    if (user) {
      fetchDirectFromDB();
    }
  }, [user, fetchDirectFromDB]);

  const isAdmin = profile?.user_role === 'admin' || profile?.user_role === 'super_admin';

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={() => navigate('/')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
            <h1 className="text-2xl font-bold">Profile Debug</h1>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* Current Profile from Hook */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Profile from Hook
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
                  </div>
                  <div>
                    <strong>Full Name:</strong> {profile?.full_name || 'N/A'}
                  </div>
                  <div>
                    <strong>Phone:</strong> {profile?.phone || 'N/A'}
                  </div>
                  <div className="flex items-center gap-2">
                    <strong>User Role:</strong> 
                    <span className={`px-2 py-1 rounded text-sm ${
                      profile?.user_role === 'super_admin' ? 'bg-red-100 text-red-800' :
                      profile?.user_role === 'admin' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {profile?.user_role || 'N/A'}
                    </span>
                    {isAdmin && <CheckCircle className="h-4 w-4 text-green-600" />}
                  </div>
                  <div>
                    <strong>Verification Status:</strong> {profile?.verification_status || 'N/A'}
                  </div>
                  <div>
                    <strong>Trust Score:</strong> {profile?.trust_score || 'N/A'}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Direct DB Profile */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Direct from Database
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <strong>Full Name:</strong> {dbProfile?.full_name || 'N/A'}
                  </div>
                  <div>
                    <strong>Phone:</strong> {dbProfile?.phone || 'N/A'}
                  </div>
                  <div className="flex items-center gap-2">
                    <strong>User Role:</strong>
                    <span className={`px-2 py-1 rounded text-sm ${
                      dbProfile?.user_role === 'super_admin' ? 'bg-red-100 text-red-800' :
                      dbProfile?.user_role === 'admin' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {dbProfile?.user_role || 'N/A'}
                    </span>
                  </div>
                  <div>
                    <strong>Verification Status:</strong> {dbProfile?.verification_status || 'N/A'}
                  </div>
                  <div>
                    <strong>Trust Score:</strong> {dbProfile?.trust_score || 'N/A'}
                  </div>
                  <div>
                    <strong>Updated At:</strong> {dbProfile?.updated_at ? new Date(dbProfile.updated_at).toLocaleString() : 'N/A'}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Actions */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-wrap gap-4">
                <Button 
                  onClick={handleRefresh} 
                  disabled={isRefreshing}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                  Refresh Profile
                </Button>
                
                {isAdmin && (
                  <Button 
                    onClick={() => navigate('/admin')}
                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                  >
                    <Shield className="h-4 w-4" />
                    Go to Admin Dashboard
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Debug Info */}
          <Card>
            <CardHeader>
              <CardTitle>Debug Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div><strong>User ID:</strong> {user?.id || 'Not logged in'}</div>
                <div><strong>User Email:</strong> {user?.email || 'N/A'}</div>
                <div><strong>Is Admin (computed):</strong> {isAdmin ? 'Yes' : 'No'}</div>
                <div><strong>Current URL:</strong> {window.location.pathname}</div>
                <div><strong>Profile Hook vs DB Match:</strong> {
                  profile?.user_role === dbProfile?.user_role ? 'Yes' : 'No'
                }</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProfileDebug;
