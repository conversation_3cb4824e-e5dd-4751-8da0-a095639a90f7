import { Database } from '../integrations/supabase/types';

// Database types
export type Property = Database['public']['Tables']['properties']['Row'];
export type PropertyInsert = Database['public']['Tables']['properties']['Insert'];
export type PropertyUpdate = Database['public']['Tables']['properties']['Update'];
export type PropertyImage = Database['public']['Tables']['property_images']['Row'];
export type PropertyUnit = Database['public']['Tables']['property_units']['Row'];
export type UnitImage = Database['public']['Tables']['unit_images']['Row'];
export type UnitInquiry = Database['public']['Tables']['unit_inquiries']['Row'];
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Favorite = Database['public']['Tables']['favorites']['Row'];

// Extended types for UI components
export interface PropertyWithImages extends Property {
  property_images: PropertyImage[];
  profiles?: Profile;
}

export interface PropertyWithUnits extends Property {
  property_images: PropertyImage[];
  profiles?: Profile;
  property_units?: PropertyUnitWithImages[];
}

export interface PropertyUnitWithImages extends PropertyUnit {
  unit_images: UnitImage[];
}

export interface PropertyWithOwner extends Property {
  profiles: Profile;
  property_images: PropertyImage[];
}

// Search and filter types
export interface SearchFilters {
  location?: string;
  county?: string;
  property_type?: string;
  maxRent?: number;
  minRent?: number;
  bedrooms?: number;
  bathrooms?: number;
  minArea?: number;
  maxArea?: number;
  amenities?: string[];
  available?: boolean;
  featured?: boolean;
}

// Room type options
export const ROOM_TYPES = [
  { value: 'single_room', label: 'Single Room' },
  { value: 'bedsitter', label: 'Bedsitter' },
  { value: 'studio', label: 'Studio' },
  { value: '1_bedroom', label: '1 Bedroom' },
  { value: '2_bedroom', label: '2 Bedroom' },
  { value: '3_bedroom', label: '3 Bedroom' },
  { value: '4_bedroom', label: '4 Bedroom' },
  { value: '5_bedroom', label: '5+ Bedroom' },
  { value: 'duplex', label: 'Duplex' },
  { value: 'penthouse', label: 'Penthouse' }
] as const;

export type RoomType = typeof ROOM_TYPES[number]['value'];

// Form types for property creation/editing
export interface PropertyFormData {
  title: string;
  description?: string;
  rent: number;
  location: string;
  county: string;
  room_type: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  amenities: string[];
  available: boolean;
  featured: boolean;
  latitude?: number;
  longitude?: number;
  formatted_address?: string;
  neighborhood?: string;
  city?: string;
  property_type?: 'single_unit' | 'multi_unit';
  building_name?: string;
  units?: Array<{
    unit_name: string;
    room_type: string;
    bedrooms: number;
    bathrooms: number;
    area: number;
    rent: number;
    deposit: number;
    is_available: boolean;
    unit_amenities: string[];
    unit_description: string;
    floor_number: number;
    unit_number: string;
  }>;
}

// Property status enums
export enum PropertyStatus {
  AVAILABLE = 'available',
  RENTED = 'rented',
  PENDING = 'pending',
  MAINTENANCE = 'maintenance'
}

// County options for Kenya (since this appears to be a Kenyan property finder)
export const KENYAN_COUNTIES = [
  'Nairobi',
  'Mombasa',
  'Kwale',
  'Kilifi',
  'Tana River',
  'Lamu',
  'Taita Taveta',
  'Garissa',
  'Wajir',
  'Mandera',
  'Marsabit',
  'Isiolo',
  'Meru',
  'Tharaka Nithi',
  'Embu',
  'Kitui',
  'Machakos',
  'Makueni',
  'Nyandarua',
  'Nyeri',
  'Kirinyaga',
  'Murang\'a',
  'Kiambu',
  'Turkana',
  'West Pokot',
  'Samburu',
  'Trans Nzoia',
  'Uasin Gishu',
  'Elgeyo Marakwet',
  'Nandi',
  'Baringo',
  'Laikipia',
  'Nakuru',
  'Narok',
  'Kajiado',
  'Kericho',
  'Bomet',
  'Kakamega',
  'Vihiga',
  'Bungoma',
  'Busia',
  'Siaya',
  'Kisumu',
  'Homa Bay',
  'Migori',
  'Kisii',
  'Nyamira'
] as const;

// Common amenities
export const COMMON_AMENITIES = [
  'Parking',
  'Security',
  'Swimming Pool',
  'Gym',
  'Garden',
  'Balcony',
  'Air Conditioning',
  'Furnished',
  'Internet/WiFi',
  'Generator',
  'Water Tank',
  'CCTV',
  'Elevator',
  'Playground',
  'Shopping Center Nearby',
  'School Nearby',
  'Hospital Nearby',
  'Public Transport'
] as const;