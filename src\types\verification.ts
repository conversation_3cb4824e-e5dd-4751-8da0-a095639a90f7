// Verification system types for landlord authentication and fraud prevention

export type UserRole = 'landlord' | 'admin' | 'super_admin';

export type VerificationStatus = 'pending' | 'under_review' | 'verified' | 'rejected' | 'suspended';

export type DocumentType = 
  | 'national_id' 
  | 'passport' 
  | 'business_registration' 
  | 'tax_certificate' 
  | 'property_title_deed' 
  | 'lease_agreement' 
  | 'utility_bill' 
  | 'bank_statement';

export type DocumentVerificationStatus = 'pending' | 'approved' | 'rejected';

export type PropertyApprovalStatus = 'pending' | 'approved' | 'rejected' | 'suspended';

export type FraudReportType = 
  | 'fake_listing' 
  | 'fake_landlord' 
  | 'scam_attempt' 
  | 'false_information' 
  | 'other';

export type FraudReportStatus = 'open' | 'investigating' | 'resolved' | 'dismissed';

export type FraudReportPriority = 'low' | 'medium' | 'high' | 'critical';

// Enhanced Profile with verification fields
export interface VerifiedProfile {
  id: string;
  full_name: string | null;
  phone: string | null;
  user_role: UserRole;
  verification_status: VerificationStatus;
  verification_submitted_at: string | null;
  verification_completed_at: string | null;
  verified_by: string | null;
  
  // Identity verification
  national_id: string | null;
  business_registration_number: string | null;
  tax_pin: string | null;
  
  // Contact verification
  phone_verified: boolean;
  email_verified: boolean;
  phone_verification_code: string | null;
  phone_verification_expires_at: string | null;
  
  // Additional landlord info
  business_name: string | null;
  business_address: string | null;
  years_in_business: number | null;
  
  // Trust and safety
  trust_score: number;
  total_properties: number;
  successful_rentals: number;
  
  // Admin notes and flags
  admin_notes: string | null;
  is_flagged: boolean;
  flagged_reason: string | null;
  flagged_at: string | null;
  flagged_by: string | null;
  
  created_at: string;
  updated_at: string;
}

// Verification Document
export interface VerificationDocument {
  id: string;
  user_id: string;
  document_type: DocumentType;
  document_url: string;
  document_name: string;
  file_size: number | null;
  mime_type: string | null;
  verification_status: DocumentVerificationStatus;
  reviewed_by: string | null;
  reviewed_at: string | null;
  rejection_reason: string | null;
  expiry_date: string | null;
  created_at: string;
  updated_at: string;
}

// Property Verification
export interface PropertyVerification {
  id: string;
  property_id: string;
  verification_status: VerificationStatus;
  ownership_verified: boolean;
  location_verified: boolean;
  photos_verified: boolean;
  verified_by: string | null;
  verified_at: string | null;
  rejection_reason: string | null;
  admin_notes: string | null;
  created_at: string;
  updated_at: string;
}

// Fraud Report
export interface FraudReport {
  id: string;
  reported_user_id: string;
  reported_property_id: string | null;
  reporter_email: string;
  reporter_phone: string | null;
  report_type: FraudReportType;
  description: string;
  evidence_urls: string[] | null;
  status: FraudReportStatus;
  priority: FraudReportPriority;
  assigned_to: string | null;
  resolution_notes: string | null;
  resolved_at: string | null;
  created_at: string;
  updated_at: string;
}

// Verification Audit Log
export interface VerificationAuditLog {
  id: string;
  user_id: string;
  action: string;
  details: Record<string, any> | null;
  performed_by: string | null;
  ip_address: string | null;
  user_agent: string | null;
  created_at: string;
}

// Form types for verification
export interface VerificationDocumentUpload {
  document_type: DocumentType;
  file: File;
  expiry_date?: string;
}

export interface LandlordVerificationForm {
  // Identity information
  national_id: string;
  business_registration_number?: string;
  tax_pin?: string;
  
  // Business information
  business_name?: string;
  business_address?: string;
  years_in_business?: number;
  
  // Documents to upload
  documents: VerificationDocumentUpload[];
}

export interface FraudReportForm {
  reported_user_id: string;
  reported_property_id?: string;
  reporter_email: string;
  reporter_phone?: string;
  report_type: FraudReportType;
  description: string;
  evidence_files?: File[];
}

export interface AdminVerificationReview {
  verification_status: VerificationStatus;
  rejection_reason?: string;
  admin_notes?: string;
  trust_score?: number;
}

export interface DocumentReview {
  verification_status: DocumentVerificationStatus;
  rejection_reason?: string;
}

// Verification statistics for admin dashboard
export interface VerificationStats {
  total_landlords: number;
  pending_verifications: number;
  verified_landlords: number;
  rejected_applications: number;
  flagged_accounts: number;
  open_fraud_reports: number;
  properties_pending_approval: number;
  average_verification_time_days: number;
}

// Client protection features
export interface TrustIndicators {
  is_verified: boolean;
  verification_badge_level: 'none' | 'basic' | 'premium' | 'gold';
  trust_score: number;
  total_properties: number;
  successful_rentals: number;
  years_active: number;
  response_rate: number;
  average_rating: number;
}

export interface PropertySafetyCheck {
  property_id: string;
  owner_verified: boolean;
  ownership_documents_verified: boolean;
  location_verified: boolean;
  photos_authentic: boolean;
  recent_activity: boolean;
  fraud_reports_count: number;
  safety_score: number; // 0-100
  warnings: string[];
  recommendations: string[];
}
