/**
 * Global caching utility for the application
 * Provides in-memory caching with TTL (Time To Live) support
 */

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class CacheManager {
  private cache = new Map<string, CacheItem<any>>();
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    // Start cleanup interval to remove expired items every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Set a cache item with TTL
   */
  set<T>(key: string, data: T, ttlMs: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs,
    });
  }

  /**
   * Get a cache item if it's still valid
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    const now = Date.now();
    const isExpired = (now - item.timestamp) > item.ttl;

    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  /**
   * Check if a cache item exists and is valid
   */
  has(key: string): boolean {
    return this.get(key) !== null;
  }

  /**
   * Delete a specific cache item
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache items
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }

  /**
   * Clean up expired cache items
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      const isExpired = (now - item.timestamp) > item.ttl;
      if (isExpired) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`Cache cleanup: removed ${expiredKeys.length} expired items`);
    }
  }

  /**
   * Destroy the cache manager and cleanup intervals
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}

// Global cache instance
export const globalCache = new CacheManager();

// Cache key generators for different data types
export const CacheKeys = {
  // User profile cache
  userProfile: (userId: string) => `profile:${userId}`,
  
  // Properties cache
  userProperties: (userId: string) => `properties:user:${userId}`,
  allProperties: () => 'properties:all',
  propertyDetail: (propertyId: string) => `property:${propertyId}`,
  
  // Admin data cache
  adminStats: () => 'admin:stats',
  pendingVerifications: () => 'admin:pending-verifications',
  
  // Verification data cache
  trustIndicators: (userId: string) => `trust:${userId}`,
  verificationDocuments: (userId: string) => `verification:docs:${userId}`,
} as const;

// Cache TTL constants (in milliseconds)
export const CacheTTL = {
  SHORT: 2 * 60 * 1000,      // 2 minutes
  MEDIUM: 5 * 60 * 1000,     // 5 minutes
  LONG: 15 * 60 * 1000,      // 15 minutes
  VERY_LONG: 60 * 60 * 1000, // 1 hour
} as const;

// Utility functions for common caching patterns
export const cacheUtils = {
  /**
   * Get or set cache with a fallback function
   */
  async getOrSet<T>(
    key: string,
    fallback: () => Promise<T>,
    ttl: number = CacheTTL.MEDIUM
  ): Promise<T> {
    const cached = globalCache.get<T>(key);
    
    if (cached !== null) {
      return cached;
    }

    const data = await fallback();
    globalCache.set(key, data, ttl);
    return data;
  },

  /**
   * Invalidate related cache keys by pattern
   */
  invalidatePattern(pattern: string): void {
    const stats = globalCache.getStats();
    const keysToDelete = stats.keys.filter(key => key.includes(pattern));
    keysToDelete.forEach(key => globalCache.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log(`Cache invalidation: removed ${keysToDelete.length} keys matching pattern "${pattern}"`);
    }
  },

  /**
   * Warm up cache with data
   */
  warmUp<T>(key: string, data: T, ttl: number = CacheTTL.MEDIUM): void {
    globalCache.set(key, data, ttl);
  },
};

// React hook for cache management
export const useCache = () => {
  return {
    get: globalCache.get.bind(globalCache),
    set: globalCache.set.bind(globalCache),
    has: globalCache.has.bind(globalCache),
    delete: globalCache.delete.bind(globalCache),
    clear: globalCache.clear.bind(globalCache),
    getStats: globalCache.getStats.bind(globalCache),
    ...cacheUtils,
  };
};
