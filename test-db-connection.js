// Test script to check database connection and tables
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://pikolpdkdvwtnphdylyp.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBpa29scGRrZHZ3dG5waGR5bHlwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwMzEyODYsImV4cCI6MjA2OTYwNzI4Nn0.V8T5skEMfB7uDR6KCPFQ--U1IzlI-MNrEA8YYFN5Pj0';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testConnection() {
  try {
    console.log('Testing Supabase connection...');
    
    // Test basic connection
    const { data, error } = await supabase.from('properties').select('count', { count: 'exact', head: true });
    
    if (error) {
      console.error('Error connecting to properties table:', error.message);
      console.error('Error details:', error);
      return;
    }
    
    console.log('✅ Successfully connected to Supabase');
    console.log('Properties table exists with', data?.length || 0, 'records');
    
    // Test fetching actual data
    const { data: properties, error: fetchError } = await supabase
      .from('properties')
      .select('*')
      .limit(5);
      
    if (fetchError) {
      console.error('Error fetching properties:', fetchError.message);
      return;
    }
    
    console.log('Sample properties:', properties);
    
  } catch (err) {
    console.error('Connection test failed:', err);
  }
}

testConnection();