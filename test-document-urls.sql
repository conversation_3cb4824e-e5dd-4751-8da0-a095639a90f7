-- Test script to verify document URL fixes
-- Run this to check if verification documents are accessible

-- Check all verification documents and their URL patterns
SELECT 
    id,
    user_id,
    document_type,
    document_name,
    verification_status,
    CASE 
        WHEN document_url LIKE 'https://%/storage/v1/object/public/verification-documents/%' THEN 'Valid Supabase URL'
        WHEN document_url LIKE 'http%' THEN 'HTTP URL'
        ELSE 'Invalid URL Format'
    END as url_type,
    LENGTH(document_url) as url_length,
    document_url,
    created_at
FROM verification_documents
ORDER BY created_at DESC
LIMIT 20;

-- Count documents by URL type
SELECT 
    CASE 
        WHEN document_url LIKE 'https://%/storage/v1/object/public/verification-documents/%' THEN 'Valid Supabase URL'
        WHEN document_url LIKE 'http%' THEN 'HTTP URL'
        ELSE 'Invalid URL Format'
    END as url_type,
    COUNT(*) as count
FROM verification_documents
GROUP BY url_type;

-- Find documents with potential issues
SELECT 
    id,
    user_id,
    document_type,
    verification_status,
    document_url
FROM verification_documents
WHERE 
    document_url IS NULL 
    OR document_url = ''
    OR LENGTH(document_url) < 10
    OR document_url NOT LIKE 'http%';

-- Check for documents pending review that might have URL issues
SELECT 
    vd.id,
    vd.user_id,
    vd.document_type,
    vd.document_name,
    vd.verification_status,
    vd.document_url,
    p.full_name,
    p.verification_status as profile_status
FROM verification_documents vd
JOIN profiles p ON vd.user_id = p.id
WHERE vd.verification_status = 'pending'
    AND p.verification_status IN ('pending', 'under_review')
ORDER BY vd.created_at DESC;
