// Test the specific query that's failing
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://pikolpdkdvwtnphdylyp.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBpa29scGRrZHZ3dG5waGR5bHlwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwMzEyODYsImV4cCI6MjA2OTYwNzI4Nn0.V8T5skEMfB7uDR6KCPFQ--U1IzlI-MNrEA8YYFN5Pj0';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testQuery() {
  try {
    console.log('Testing the problematic query...');
    
    // Test the exact query from useProperties
    const { data, error } = await supabase
      .from('properties')
      .select(`
        *,
        property_images:property_images (
          id,
          property_id,
          image_url,
          is_primary,
          created_at
        ),
        profiles!user_id (
          id,
          full_name,
          phone
        )
      `)
      .eq('available', true)
      .order('created_at', { ascending: false });
      
    if (error) {
      console.error('❌ Query failed:', error.message);
      console.error('Error details:', error);
      
      // Try a simpler query without the profiles join
      console.log('\nTrying simpler query without profiles...');
      const { data: simpleData, error: simpleError } = await supabase
        .from('properties')
        .select(`
          *,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          )
        `)
        .eq('available', true)
        .order('created_at', { ascending: false })
        .limit(2);
        
      if (simpleError) {
        console.error('❌ Simple query also failed:', simpleError.message);
      } else {
        console.log('✅ Simple query succeeded');
        console.log('Sample data:', JSON.stringify(simpleData, null, 2));
      }
      
      return;
    }
    
    console.log('✅ Query succeeded');
    console.log('Data:', JSON.stringify(data, null, 2));
    
  } catch (err) {
    console.error('Test failed:', err);
  }
}

testQuery();