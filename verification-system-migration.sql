-- ============================================================================
-- LANDLORD VERIFICATION & CLIENT PROTECTION SYSTEM MIGRATION
-- ============================================================================
-- This migration adds comprehensive verification and fraud protection features
-- Run this AFTER the existing complete-database-setup.sql for existing databases
-- For NEW installations, use complete-database-setup.sql which includes everything
-- ============================================================================

-- ============================================================================
-- STEP 1: UPDATE EXISTING PROFILES TABLE
-- ============================================================================

-- Add verification fields to existing profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS user_role TEXT DEFAULT 'landlord' CHECK (user_role IN ('landlord', 'admin', 'super_admin')),
ADD COLUMN IF NOT EXISTS verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'under_review', 'verified', 'rejected', 'suspended')),
ADD COLUMN IF NOT EXISTS verification_submitted_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS verification_completed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS verified_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS national_id TEXT,
ADD COLUMN IF NOT EXISTS business_registration_number TEXT,
ADD COLUMN IF NOT EXISTS tax_pin TEXT,
ADD COLUMN IF NOT EXISTS phone_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS phone_verification_code TEXT,
ADD COLUMN IF NOT EXISTS phone_verification_expires_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS business_name TEXT,
ADD COLUMN IF NOT EXISTS business_address TEXT,
ADD COLUMN IF NOT EXISTS years_in_business INTEGER,
ADD COLUMN IF NOT EXISTS trust_score DECIMAL(3,2) DEFAULT 0.00 CHECK (trust_score >= 0 AND trust_score <= 5.00),
ADD COLUMN IF NOT EXISTS total_properties INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS successful_rentals INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS admin_notes TEXT,
ADD COLUMN IF NOT EXISTS is_flagged BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS flagged_reason TEXT,
ADD COLUMN IF NOT EXISTS flagged_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS flagged_by UUID REFERENCES auth.users(id);

-- ============================================================================
-- STEP 2: UPDATE EXISTING PROPERTIES TABLE
-- ============================================================================

-- Add verification fields to existing properties table
ALTER TABLE public.properties 
ADD COLUMN IF NOT EXISTS approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected', 'suspended')),
ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS rejection_reason TEXT,
ADD COLUMN IF NOT EXISTS requires_verification BOOLEAN DEFAULT TRUE;

-- ============================================================================
-- STEP 3: CREATE NEW VERIFICATION TABLES
-- ============================================================================

-- Create verification documents table
CREATE TABLE IF NOT EXISTS public.verification_documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  document_type TEXT NOT NULL CHECK (document_type IN ('national_id', 'passport', 'business_registration', 'tax_certificate', 'property_title_deed', 'lease_agreement', 'utility_bill', 'bank_statement')),
  document_url TEXT NOT NULL,
  document_name TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'approved', 'rejected')),
  reviewed_by UUID REFERENCES auth.users(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  expiry_date DATE, -- For documents that expire
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create property verification table
CREATE TABLE IF NOT EXISTS public.property_verification (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE NOT NULL,
  verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'under_review', 'verified', 'rejected')),
  ownership_verified BOOLEAN DEFAULT FALSE,
  location_verified BOOLEAN DEFAULT FALSE,
  photos_verified BOOLEAN DEFAULT FALSE,
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  admin_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create fraud reports table
CREATE TABLE IF NOT EXISTS public.fraud_reports (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  reported_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  reported_property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
  reporter_email TEXT NOT NULL,
  reporter_phone TEXT,
  report_type TEXT NOT NULL CHECK (report_type IN ('fake_listing', 'fake_landlord', 'scam_attempt', 'false_information', 'other')),
  description TEXT NOT NULL,
  evidence_urls TEXT[], -- Array of URLs to evidence files
  status TEXT DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'dismissed')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  assigned_to UUID REFERENCES auth.users(id),
  resolution_notes TEXT,
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create verification audit log
CREATE TABLE IF NOT EXISTS public.verification_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  action TEXT NOT NULL, -- 'document_uploaded', 'verification_approved', 'verification_rejected', etc.
  details JSONB,
  performed_by UUID REFERENCES auth.users(id),
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- ============================================================================
-- STEP 4: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Indexes for verification documents
CREATE INDEX IF NOT EXISTS idx_verification_documents_user_id ON verification_documents(user_id);
CREATE INDEX IF NOT EXISTS idx_verification_documents_type ON verification_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_verification_documents_status ON verification_documents(verification_status);

-- Indexes for property verification
CREATE INDEX IF NOT EXISTS idx_property_verification_property_id ON property_verification(property_id);
CREATE INDEX IF NOT EXISTS idx_property_verification_status ON property_verification(verification_status);

-- Indexes for fraud reports
CREATE INDEX IF NOT EXISTS idx_fraud_reports_reported_user ON fraud_reports(reported_user_id);
CREATE INDEX IF NOT EXISTS idx_fraud_reports_property ON fraud_reports(reported_property_id);
CREATE INDEX IF NOT EXISTS idx_fraud_reports_status ON fraud_reports(status);
CREATE INDEX IF NOT EXISTS idx_fraud_reports_priority ON fraud_reports(priority);

-- Indexes for audit log
CREATE INDEX IF NOT EXISTS idx_verification_audit_user_id ON verification_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_verification_audit_action ON verification_audit_log(action);
CREATE INDEX IF NOT EXISTS idx_verification_audit_created_at ON verification_audit_log(created_at);

-- Indexes for profiles verification fields
CREATE INDEX IF NOT EXISTS idx_profiles_verification_status ON profiles(verification_status);
CREATE INDEX IF NOT EXISTS idx_profiles_user_role ON profiles(user_role);
CREATE INDEX IF NOT EXISTS idx_profiles_trust_score ON profiles(trust_score);
CREATE INDEX IF NOT EXISTS idx_profiles_is_flagged ON profiles(is_flagged);

-- Indexes for properties verification fields
CREATE INDEX IF NOT EXISTS idx_properties_approval_status ON properties(approval_status);

-- ============================================================================
-- STEP 5: ENABLE ROW LEVEL SECURITY ON NEW TABLES
-- ============================================================================

ALTER TABLE public.verification_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_verification ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fraud_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.verification_audit_log ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 6: CREATE RLS POLICIES FOR NEW TABLES
-- ============================================================================

-- Policies for verification documents
CREATE POLICY "Users can view their own verification documents"
ON verification_documents FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own verification documents"
ON verification_documents FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own verification documents"
ON verification_documents FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Admins can view all verification documents"
ON verification_documents FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND user_role IN ('admin', 'super_admin')
    )
);

CREATE POLICY "Admins can update verification documents"
ON verification_documents FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND user_role IN ('admin', 'super_admin')
    )
);

-- Policies for property verification
CREATE POLICY "Property owners can view their property verification"
ON property_verification FOR SELECT
USING (
    property_id IN (
        SELECT id FROM properties WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Admins can manage all property verifications"
ON property_verification FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND user_role IN ('admin', 'super_admin')
    )
);

-- Policies for fraud reports
CREATE POLICY "Anyone can insert fraud reports"
ON fraud_reports FOR INSERT
WITH CHECK (true);

CREATE POLICY "Admins can view and manage fraud reports"
ON fraud_reports FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND user_role IN ('admin', 'super_admin')
    )
);

-- Policies for verification audit log
CREATE POLICY "Admins can view audit logs"
ON verification_audit_log FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND user_role IN ('admin', 'super_admin')
    )
);

CREATE POLICY "System can insert audit logs"
ON verification_audit_log FOR INSERT
WITH CHECK (true);

-- ============================================================================
-- STEP 7: CREATE STORAGE BUCKET FOR VERIFICATION DOCUMENTS
-- ============================================================================

-- Create storage bucket for verification documents (private)
INSERT INTO storage.buckets (id, name, public) 
VALUES ('verification-documents', 'verification-documents', false)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for verification documents
CREATE POLICY "Users can upload their own verification documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'verification-documents' 
    AND auth.role() = 'authenticated'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own verification documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'verification-documents' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Admins can view all verification documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'verification-documents' 
    AND EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND user_role IN ('admin', 'super_admin')
    )
  );

CREATE POLICY "Users can update their own verification documents" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'verification-documents' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own verification documents" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'verification-documents' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- ============================================================================
-- STEP 8: CREATE FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Create function to log verification actions
CREATE OR REPLACE FUNCTION public.log_verification_action()
RETURNS TRIGGER AS $$
DECLARE
  target_user_id UUID;
BEGIN
  -- Determine the user_id based on the table structure
  IF TG_TABLE_NAME = 'profiles' THEN
    target_user_id := COALESCE(NEW.id, OLD.id);
  ELSE
    target_user_id := COALESCE(NEW.user_id, OLD.user_id);
  END IF;

  INSERT INTO public.verification_audit_log (
    user_id,
    action,
    details,
    performed_by
  ) VALUES (
    target_user_id,
    TG_OP || '_' || TG_TABLE_NAME,
    jsonb_build_object(
      'old', to_jsonb(OLD),
      'new', to_jsonb(NEW)
    ),
    auth.uid()
  );

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for audit logging
DROP TRIGGER IF EXISTS log_verification_document_changes ON public.verification_documents;
CREATE TRIGGER log_verification_document_changes
  AFTER INSERT OR UPDATE OR DELETE ON public.verification_documents
  FOR EACH ROW EXECUTE PROCEDURE public.log_verification_action();

DROP TRIGGER IF EXISTS log_profile_verification_changes ON public.profiles;
CREATE TRIGGER log_profile_verification_changes
  AFTER UPDATE OF verification_status ON public.profiles
  FOR EACH ROW EXECUTE PROCEDURE public.log_verification_action();

-- Create triggers for updated_at timestamps on new tables
DROP TRIGGER IF EXISTS handle_updated_at ON public.verification_documents;
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.verification_documents
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

DROP TRIGGER IF EXISTS handle_updated_at ON public.property_verification;
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.property_verification
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

DROP TRIGGER IF EXISTS handle_updated_at ON public.fraud_reports;
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.fraud_reports
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

-- ============================================================================
-- STEP 9: INSERT SAMPLE DATA (OPTIONAL)
-- ============================================================================

-- Create a sample admin user (update the UUID with an actual user ID from your auth.users table)
-- INSERT INTO public.profiles (id, full_name, user_role, verification_status, trust_score)
-- VALUES ('your-admin-user-uuid-here', 'System Administrator', 'super_admin', 'verified', 5.0)
-- ON CONFLICT (id) DO UPDATE SET user_role = 'super_admin', verification_status = 'verified';

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Verification system migration completed successfully!
-- 
-- What was added:
-- - Enhanced profiles table with verification fields
-- - Enhanced properties table with approval status
-- - 4 new tables for verification system
-- - Comprehensive indexes for performance
-- - Row Level Security policies
-- - Storage bucket for verification documents
-- - Audit logging functions and triggers
-- 
-- Next steps:
-- 1. Update your application to use the new verification features
-- 2. Create admin users by updating their user_role in profiles table
-- 3. Test the verification workflow
-- 4. Configure any third-party verification services (optional)
